import { Message } from "@aws-sdk/client-sqs";
import { Injectable, Logger } from "@nestjs/common";
import { SqsMessageHandler } from "@ssut/nestjs-sqs";
import { PaginationInput } from "../../common/dto/pagination.input";
import { PaginationService } from "../../common/services/pagination.service";
import { ShopifyService } from "../../shopify/shopify.service";
import { VariantRepository } from "../../variants/repositories/variant.repository";
import { PaginatedProductsResponseDto } from "../dto/paginated-products-response.dto";
import { ProductWhereDto } from "../dto/product-where.dto";
import { ProductRepository } from "../repositories/product.repository";
import {
  IExcelProductMessage,
  isExcelProductMessage,
} from "../types/message.types";

@Injectable()
export class ProductsService {
  private readonly logger = new Logger(ProductsService.name);

  constructor(
    private readonly productRepository: ProductRepository,
    private readonly paginationService: PaginationService,
    private readonly shopifyService: ShopifyService,
    private readonly variantRepository: VariantRepository,
  ) {}

  async findAll(whereInput: ProductWhereDto) {
    return this.productRepository.findAll(whereInput);
  }

  async findAllPaginated(
    pagination: PaginationInput,
    whereInput?: ProductWhereDto,
    quickSearchQuery?: string,
  ): Promise<PaginatedProductsResponseDto> {
    const paginationParams =
      this.paginationService.getPaginationParams(pagination);
    const { totalCount, products: data } =
      await this.productRepository.findAllPaginated(
        paginationParams,
        whereInput,
        quickSearchQuery,
      );

    const meta = this.paginationService.getPaginationMeta(
      pagination,
      totalCount,
    );

    return { meta, data };
  }

  findOne(id: number) {
    return this.productRepository.findOne(id);
  }

  remove(id: number) {
    return this.productRepository.remove(id);
  }

  @SqsMessageHandler("excel-products", false)
  async processExcelProduct(message: Message) {
    this.logger.log(`Processing Excel product: ${message.MessageId}`);

    if (!message.Body) {
      throw new Error("Message body is empty");
    }

    let parsedData: unknown;
    try {
      parsedData = JSON.parse(message.Body);
    } catch (parseError) {
      this.logger.error(`Failed to parse message body: ${parseError}`);
      throw new Error("Invalid JSON in message body");
    }

    if (!isExcelProductMessage(parsedData)) {
      this.logger.error("Invalid message structure for Excel product");
      throw new Error("Invalid message structure for Excel product");
    }

    const messageData: IExcelProductMessage = parsedData;

    try {
      // Send to Shopify first
      await this.sendToShopify(messageData);

      // Then persist to database
      await this.persistToDb(messageData);

      this.logger.log(
        `Successfully processed product: ${messageData.productName}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to process product ${messageData.productName}: ${error}`,
      );
      throw error;
    }
  }

  private async sendToShopify(message: {
    productName: string;
    variants: { variantName: string; sku: string; size: string }[];
    sizes: string[];
    shopKey: string;
  }): Promise<void> {
    const productSet = {
      title: message.productName,
      productOptions: message.sizes.length
        ? [
            {
              name: "Size",
              position: 1,
              values: message.sizes.map((name) => ({ name })),
            },
          ]
        : undefined,
      variants: message.variants.map((v) => ({
        optionValues: [{ optionName: "Size", name: v.size }],
        sku: v.sku,
      })),
    };

    try {
      const result = await this.shopifyService.bulkSetProduct(
        message.shopKey,
        productSet,
        false,
      );
      this.logger.debug(`Shopify productSet result: ${JSON.stringify(result)}`);
    } catch (err) {
      this.logger.error(
        `Shopify productSet failed for ${message.productName}: ${String(err)}`,
      );
      throw err;
    }
  }

  private async persistToDb(message: {
    productName: string;
    variants: { variantName: string; sku: string }[];
  }): Promise<void> {
    try {
      const product = await this.productRepository.upsertByName(
        message.productName,
      );

      for (const variant of message.variants) {
        await this.variantRepository.upsertByProductAndName({
          productId: product.id,
          variantName: variant.variantName,
          sku: variant.sku,
        });
      }

      this.logger.log(`Persisted product to DB: ${message.productName}`);
    } catch (dbErr) {
      this.logger.error(
        `DB persist failed for ${message.productName}: ${String(dbErr)}`,
      );
      throw dbErr;
    }
  }
}

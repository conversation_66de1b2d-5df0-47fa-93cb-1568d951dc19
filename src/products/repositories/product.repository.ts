import { Injectable } from "@nestjs/common";
import { plainToInstance } from "class-transformer";
import { PaginationParameters } from "../../common/dto/pagination-parameters.dto";
import { PrismaService } from "../../common/services/prisma.service";
import { QueryMapperService } from "../../common/services/query-mapper.service";
import { ProductResponseDto } from "../dto/product-response.dto";
import { ProductWhereDto } from "../dto/product-where.dto";

@Injectable()
export class ProductRepository {
  constructor(
    private readonly prisma: PrismaService,
    private readonly queryMapperService: QueryMapperService,
  ) {}

  async findAll(whereInput?: ProductWhereDto): Promise<ProductResponseDto[]> {
    const where = whereInput
      ? this.queryMapperService.mapWhereInput(whereInput, ["id", "shopId"])
      : undefined;

    const products = await this.prisma.product.findMany({
      where,
    });

    return plainToInstance(ProductResponseDto, products);
  }

  async findAllPaginated(
    pagination: PaginationParameters,
    whereInput?: ProductWhereDto,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _quickSearchQuery?: string,
  ): Promise<{ products: ProductResponseDto[]; totalCount: number }> {
    const where = whereInput
      ? this.queryMapperService.mapWhereInput(whereInput, ["id", "shopId"])
      : undefined;

    const query = {
      where,
    };
    const [totalCount, products] = await Promise.all([
      this.prisma.product.count(query),
      this.prisma.product.findMany({
        ...query,
        ...pagination,
        orderBy: { id: "asc" },
      }),
    ]);

    return {
      products: plainToInstance(ProductResponseDto, products),
      totalCount,
    };
  }

  async findOne(id: number): Promise<ProductResponseDto | null> {
    const product = await this.prisma.product.findUnique({
      where: { id },
    });
    return product ? plainToInstance(ProductResponseDto, product) : null;
  }

  async create(productName: string): Promise<ProductResponseDto> {
    const product = await this.prisma.product.create({
      data: { productName, collectionId: 1 },
    });
    return plainToInstance(ProductResponseDto, product);
  }

  async findByName(productName: string): Promise<ProductResponseDto | null> {
    const product = await this.prisma.product.findFirst({
      where: { productName },
    });
    return product ? plainToInstance(ProductResponseDto, product) : null;
  }

  async upsertByName(productName: string): Promise<ProductResponseDto> {
    const existing = await this.prisma.product.findFirst({
      where: { productName },
    });
    if (existing) {
      // Nothing to update currently besides name; return existing as-is
      return plainToInstance(ProductResponseDto, existing);
    }
    const created = await this.prisma.product.create({
      data: { productName, collectionId: 1 },
    });
    return plainToInstance(ProductResponseDto, created);
  }

  async remove(id: number): Promise<ProductResponseDto> {
    const removed = await this.prisma.product.delete({
      where: { id },
    });
    return plainToInstance(ProductResponseDto, removed);
  }

  delete(id: number): Promise<ProductResponseDto> {
    return this.remove(id);
  }
}

import { Injectable } from "@nestjs/common";
import { plainToInstance } from "class-transformer";
import { PaginationParameters } from "../../common/dto/pagination-parameters.dto";
import { PrismaService } from "../../common/services/prisma.service";
import { QueryMapperService } from "../../common/services/query-mapper.service";
import { VariantResponseDto } from "../dto/variant-response.dto";
import { VariantWhereDto } from "../dto/variant-where.dto";

@Injectable()
export class VariantRepository {
  constructor(
    private readonly prisma: PrismaService,
    private readonly queryMapperService: QueryMapperService,
  ) {}

  async findAll(
    whereInput?: VariantWhereDto,
    productId?: bigint,
  ): Promise<VariantResponseDto[]> {
    let where: Record<string, unknown> = {};

    if (whereInput) {
      where = this.queryMapperService.mapWhereInput(whereInput, [
        "id",
        "price",
        "compareAtPrice",
        "productId",
      ]);
    }

    if (productId !== undefined) {
      where.productId = productId;
    }

    const variants = await this.prisma.variant.findMany({
      where,
    });
    return plainToInstance(VariantResponseDto, variants);
  }

  async findAllPaginated(
    pagination: PaginationParameters,
    whereInput?: VariantWhereDto,
    quickSearchQuery?: string,
  ): Promise<{ variants: VariantResponseDto[]; totalCount: number }> {
    if (quickSearchQuery) {
      return this.searchVariants(pagination, quickSearchQuery);
    }
    const where = whereInput
      ? this.queryMapperService.mapWhereInput(whereInput, [
          "id",
          "price",
          "compareAtPrice",
          "productId",
        ])
      : undefined;

    const query = {
      where,
    };

    const [totalCount, variants] = await Promise.all([
      this.prisma.variant.count(query),
      this.prisma.variant.findMany({
        ...query,
        ...pagination,
        orderBy: { id: "asc" },
      }),
    ]);

    return {
      variants: plainToInstance(VariantResponseDto, variants),
      totalCount,
    };
  }

  private async searchVariants(
    pagination: PaginationParameters,
    query?: string,
  ): Promise<{ variants: VariantResponseDto[]; totalCount: number }> {
    // Clean the query by removing special characters and normalizing whitespace
    const cleanedQuery = (query ?? "")
      .trim()
      .replace(/[^\w\s]/g, " ")
      .replace(/\s+/g, " ");

    const searchTerms = cleanedQuery
      .split(" ")
      .filter((term) => term.length > 0);

    const tsQuery = searchTerms.map((word) => `${word}:*`).join(" & ");

    const [countResult, variants] = await Promise.all([
      this.prisma.$queryRaw<{ count: number }[]>`
          SELECT COUNT(*)
          FROM "variants" v
          WHERE search_vector @@ to_tsquery('english', ${tsQuery})
      `,
      // Don't use `SELECT v.*`, prisma doesn't know how to interpret PSQL Vector Types and also we don't need it in the result set.
      this.prisma.$queryRaw<
        {
          id: number;
          variant_name: string;
          sku: string;
          product_id: number;
          created_at: Date;
          updated_at: Date;
        }[]
      >`
          SELECT v.id,
                 v.variant_name,
                 v.sku,
                 v.product_id,
                 v.created_at,
                 v.updated_at
          FROM "variants" v
          WHERE search_vector @@ to_tsquery('english', ${tsQuery})
          ORDER BY v.id ASC
          LIMIT ${pagination.take} OFFSET ${pagination.skip}
      `,
    ]);

    const totalCount = countResult?.[0]?.count ?? 0;

    return {
      variants: plainToInstance(
        VariantResponseDto,
        variants.map((v) => ({
          id: v.id,
          variantName: v.variant_name,
          sku: v.sku,
          productId: v.product_id,
          createdAt: v.created_at,
          updatedAt: v.updated_at,
        })),
      ),
      totalCount,
    };
  }

  async findOne(id: number): Promise<VariantResponseDto> {
    const variant = await this.prisma.variant.findUnique({
      where: { id: id },
    });
    return plainToInstance(VariantResponseDto, variant);
  }

  async remove(id: number): Promise<VariantResponseDto> {
    const removed = await this.prisma.variant.delete({
      where: { id: id },
    });
    return plainToInstance(VariantResponseDto, removed);
  }

  delete(id: number): Promise<VariantResponseDto> {
    return this.remove(id);
  }

  async create({
    productId,
    variantName,
    sku,
  }: {
    productId: number;
    variantName: string;
    sku: string;
  }): Promise<VariantResponseDto> {
    const variant = await this.prisma.variant.create({
      data: { productId, variantName, sku },
    });
    return plainToInstance(VariantResponseDto, variant);
  }

  async upsertByProductAndName({
    productId,
    variantName,
    sku,
  }: {
    productId: number;
    variantName: string;
    sku: string;
  }): Promise<VariantResponseDto> {
    const existing = await this.prisma.variant.findFirst({
      where: { productId, variantName },
    });
    if (existing) {
      const updated = await this.prisma.variant.update({
        where: { id: existing.id },
        data: { sku },
      });
      return plainToInstance(VariantResponseDto, updated);
    }
    const created = await this.prisma.variant.create({
      data: { productId, variantName, sku },
    });
    return plainToInstance(VariantResponseDto, created);
  }

  async doesSkuExist(sku: string) {
    const variant = await this.prisma.variant.findUnique({
      where: { sku },
    });
    return variant !== null;
  }
}

import { reactRouter } from "@react-router/dev/vite";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";

export default defineConfig({
  base: "/productmaster/",
  ssr: {
    noExternal: [/^@mui\//], // make sure MUI packages are bundled, can help sometimes
  },
  plugins: [reactRouter(), tsconfigPaths()],
  define: {
    "process.env": process.env,
  },
  ssr: {
    noExternal: [/^@mui\//], // make sure MUI packages are bundled, can help sometimes
  },
});

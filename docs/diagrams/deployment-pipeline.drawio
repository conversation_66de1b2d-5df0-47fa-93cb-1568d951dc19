<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-27T10:00:00.000Z" agent="5.0" etag="def456" version="24.7.17">
  <diagram name="Deployment Pipeline" id="deployment-pipeline">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Title -->
        <mxCell id="title" value="Ecom360 Product Master - CI/CD Deployment Pipeline" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="20" width="569" height="30" as="geometry" />
        </mxCell>
        
        <!-- GitHub Section -->
        <mxCell id="github-section" value="Source Control (GitHub)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="80" width="200" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="github-repo" value="GitHub Repository&#xa;fe-product-master&#xa;Triggers:&#xa;• Push to main&#xa;• Pull requests" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="80" y="140" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- GitHub Actions -->
        <mxCell id="github-actions" value="GitHub Actions Workflow&#xa;(.github/workflows/deploy-infrastructure.yaml)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="320" y="80" width="760" height="40" as="geometry" />
        </mxCell>
        
        <!-- Lint Stage -->
        <mxCell id="lint-stage" value="Lint Stage&#xa;Job: lint&#xa;• cfn-lint ci/aws/ecr.yaml&#xa;• cfn-lint ci/aws/fe-product-master.yaml" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="320" y="140" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- Deploy Stage -->
        <mxCell id="deploy-stage" value="Deploy Stage&#xa;Job: deploy&#xa;Environment: production&#xa;Condition: main branch only" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="540" y="140" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- AWS Credentials -->
        <mxCell id="aws-creds" value="AWS Authentication&#xa;Role: GitHubDeployRole&#xa;Region: eu-central-1&#xa;OIDC Token Exchange" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="760" y="140" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- Script Execution -->
        <mxCell id="script-section" value="Deployment Scripts Execution" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="260" width="1000" height="40" as="geometry" />
        </mxCell>
        
        <!-- ECR Deployment -->
        <mxCell id="ecr-deploy" value="1. ECR Repository Setup&#xa;Script: ci/scripts/cloudformation/deploy-ecr.sh&#xa;Template: ci/aws/ecr.yaml&#xa;• Creates ECR repository&#xa;• Sets lifecycle policies&#xa;• Enables image scanning" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;align=left;" vertex="1" parent="1">
          <mxGeometry x="80" y="320" width="240" height="100" as="geometry" />
        </mxCell>
        
        <!-- Build and Push -->
        <mxCell id="build-push" value="2. Build &amp; Push Docker Image&#xa;Script: ci/scripts/build-and-push.sh&#xa;Dockerfile: ci/Dockerfile&#xa;• Retrieves secrets from AWS&#xa;• Multi-stage Docker build&#xa;• Pushes to ECR with tags" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;align=left;" vertex="1" parent="1">
          <mxGeometry x="340" y="320" width="240" height="100" as="geometry" />
        </mxCell>
        
        <!-- App Deployment -->
        <mxCell id="app-deploy" value="3. Application Deployment&#xa;Script: ci/scripts/cloudformation/deploy-fe-product-master.sh&#xa;Template: ci/aws/fe-product-master.yaml&#xa;• Deploys ECS infrastructure&#xa;• Updates service with new image" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;align=left;" vertex="1" parent="1">
          <mxGeometry x="600" y="320" width="240" height="100" as="geometry" />
        </mxCell>
        
        <!-- Monitoring -->
        <mxCell id="monitoring" value="4. Deployment Monitoring&#xa;• CloudWatch Logs&#xa;• ECS Service Health&#xa;• ALB Target Group Health&#xa;• Circuit Breaker Protection" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;align=left;" vertex="1" parent="1">
          <mxGeometry x="860" y="320" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- Build Process Detail -->
        <mxCell id="build-detail" value="Docker Build Process Detail" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="460" width="1000" height="40" as="geometry" />
        </mxCell>
        
        <!-- Build Stage 1 -->
        <mxCell id="build-stage1" value="Build Stage (Node.js 22 Alpine)&#xa;• Install pnpm&#xa;• Configure GitHub Package Registry&#xa;• Install dependencies (pnpm install)&#xa;• Build React app (pnpm run build)&#xa;• Generate optimized static files" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;align=left;" vertex="1" parent="1">
          <mxGeometry x="80" y="520" width="240" height="120" as="geometry" />
        </mxCell>
        
        <!-- Build Stage 2 -->
        <mxCell id="build-stage2" value="Production Stage (Nginx Alpine)&#xa;• Copy nginx.conf configuration&#xa;• Copy built React files&#xa;• Install curl for health checks&#xa;• Expose port 8080&#xa;• Configure SPA routing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;align=left;" vertex="1" parent="1">
          <mxGeometry x="340" y="520" width="240" height="120" as="geometry" />
        </mxCell>
        
        <!-- Environment Variables -->
        <mxCell id="env-vars" value="Environment Variables Injection&#xa;Build Args:&#xa;• GITHUB_TOKEN (from Secrets Manager)&#xa;• VITE_COGNITO_USER_POOL_ID&#xa;• VITE_COGNITO_USER_POOL_CLIENT_ID&#xa;• VITE_GRAPHQL_BACKEND_URL" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;align=left;" vertex="1" parent="1">
          <mxGeometry x="600" y="520" width="240" height="120" as="geometry" />
        </mxCell>
        
        <!-- Deployment Result -->
        <mxCell id="deploy-result" value="Deployment Result&#xa;• ECS Service Updated&#xa;• Rolling Deployment&#xa;• Health Checks Pass&#xa;• Application Available at:&#xa;  /productmaster route" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;align=left;" vertex="1" parent="1">
          <mxGeometry x="860" y="520" width="200" height="120" as="geometry" />
        </mxCell>
        
        <!-- Configuration Files Reference -->
        <mxCell id="config-ref" value="Key Configuration Files Referenced" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="680" width="1000" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="config-files" value="CI/CD Configuration:&#xa;• .github/workflows/deploy-infrastructure.yaml&#xa;• ci/scripts/cloudformation/deploy-ecr.sh&#xa;• ci/scripts/cloudformation/deploy-fe-product-master.sh&#xa;• ci/scripts/build-and-push.sh" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;align=left;" vertex="1" parent="1">
          <mxGeometry x="80" y="740" width="300" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="infra-files" value="Infrastructure as Code:&#xa;• ci/aws/ecr.yaml&#xa;• ci/aws/fe-product-master.yaml&#xa;• ci/Dockerfile&#xa;• ci/nginx.conf" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;align=left;" vertex="1" parent="1">
          <mxGeometry x="400" y="740" width="300" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="app-files" value="Application Configuration:&#xa;• package.json&#xa;• vite.config.ts&#xa;• tsconfig.json&#xa;• react-router.config.ts" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;align=left;" vertex="1" parent="1">
          <mxGeometry x="720" y="740" width="300" height="80" as="geometry" />
        </mxCell>
        
        <!-- Arrows -->
        <mxCell id="arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="github-repo" target="lint-stage">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="lint-stage" target="deploy-stage">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="deploy-stage" target="aws-creds">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="ecr-deploy" target="build-push">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="build-push" target="app-deploy">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="app-deploy" target="monitoring">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="build-stage1" target="build-stage2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Labels -->
        <mxCell id="label1" value="Trigger" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="290" y="150" width="50" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="label2" value="Sequential" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="320" y="350" width="60" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="label3" value="Multi-stage" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="320" y="550" width="70" height="30" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

<mxfile host="Electron" modified="2025-08-27T14:00:42.748Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/21.7.5 Chrome/114.0.5735.289 Electron/25.8.1 Safari/537.36" etag="kLrCmnrsdZJZCVBmfyYp" version="21.7.5" type="device">
  <diagram name="Simple Architecture" id="simple-architecture">
    <mxGraphModel dx="1114" dy="841" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="title" value="&lt;span style=&quot;font-weight: normal;&quot;&gt;&lt;font style=&quot;font-size: 32px;&quot;&gt;Frontend Architecture&lt;/font&gt;&lt;/span&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="20" width="310" height="50" as="geometry" />
        </mxCell>
        <mxCell id="Z3es_wnvyDqwGdqayqaq-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user" target="internet">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="user" value="Users" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="110" width="100" height="50" as="geometry" />
        </mxCell>
        <mxCell id="Z3es_wnvyDqwGdqayqaq-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="internet" target="alb">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="internet" value="&lt;font style=&quot;font-size: 12px;&quot;&gt;ApiGateway&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="280" y="110" width="100" height="50" as="geometry" />
        </mxCell>
        <mxCell id="Z3es_wnvyDqwGdqayqaq-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="alb" target="ecs">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="alb" value="Load Balancer&lt;br&gt;AWS ALB&lt;br&gt;Route: /{defaultroute}" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="460" y="100" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="ecs" value="Container Platform&lt;br&gt;AWS ECS Fargate&lt;br&gt;1 instance&lt;br&gt;React + Nginx" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="690" y="100" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="Z3es_wnvyDqwGdqayqaq-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="github" target="cicd">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="github" value="Source Code&#xa;GitHub Repository" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="110" y="350" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Z3es_wnvyDqwGdqayqaq-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="cicd" target="ecr">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="cicd" value="CI/CD Pipeline&lt;br&gt;GitHub Actions&lt;br&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="290" y="350" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Z3es_wnvyDqwGdqayqaq-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="ecr" target="cloudformation">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ecr" value="AWS ECR&lt;br&gt;Version Docker Images" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="490" y="350" width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cloudformation" value="Infrastructure&#xa;AWS CloudFormation&#xa;Infrastructure as Code" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="670" y="350" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="graphql" value="GraphQL API&#xa;Product Data Backend&#xa;ecomthreesixty.com" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="880" y="120" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="cognito" value="Authentication&#xa;AWS Cognito&#xa;User Management" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="880" y="30" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="secrets" value="Configuration&#xa;AWS Secrets Manager&#xa;Environment Variables" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="880" y="215" width="150" height="70" as="geometry" />
        </mxCell>
        <mxCell id="label1" value="HTTPS" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="1" vertex="1">
          <mxGeometry x="210" y="110" width="40" height="20" as="geometry" />
        </mxCell>
        <mxCell id="Z3es_wnvyDqwGdqayqaq-1" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="cognito">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="840" y="140" as="sourcePoint" />
            <mxPoint x="890" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Z3es_wnvyDqwGdqayqaq-2" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" edge="1" parent="1" target="graphql">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="840" y="145" as="sourcePoint" />
            <mxPoint x="890" y="95" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Z3es_wnvyDqwGdqayqaq-3" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.75;exitDx=0;exitDy=0;" edge="1" parent="1" source="ecs" target="secrets">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="810" y="250" as="sourcePoint" />
            <mxPoint x="860" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Z3es_wnvyDqwGdqayqaq-7" value="&lt;font style=&quot;font-size: 32px;&quot;&gt;Deployment Process&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="100" y="270" width="310" height="45" as="geometry" />
        </mxCell>
        <mxCell id="Z3es_wnvyDqwGdqayqaq-11" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0.25;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1" source="cloudformation" target="internet">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="710" y="350" as="sourcePoint" />
            <mxPoint x="760" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Z3es_wnvyDqwGdqayqaq-12" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1" source="cloudformation" target="alb">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="788" y="345" as="sourcePoint" />
            <mxPoint x="410" y="155" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Z3es_wnvyDqwGdqayqaq-13" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.573;exitY=-0.017;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="cloudformation" target="ecs">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="760" y="335" as="sourcePoint" />
            <mxPoint x="810" y="285" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-27T10:00:00.000Z" agent="5.0" etag="abc123" version="24.7.17">
  <diagram name="Architecture Overview" id="architecture-overview">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Title -->
        <mxCell id="title" value="Ecom360 Product Master Frontend - Architecture Overview" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="20" width="569" height="30" as="geometry" />
        </mxCell>
        
        <!-- User Layer -->
        <mxCell id="user-layer" value="User Layer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="80" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="browser" value="Web Browser&#xa;(React SPA)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="80" y="140" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Load Balancer -->
        <mxCell id="alb" value="Application Load Balancer&#xa;(ALB)&#xa;Route: /productmaster*" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="280" y="140" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- API Gateway -->
        <mxCell id="api-gateway" value="AWS API Gateway v2&#xa;Routes:&#xa;GET /productmaster&#xa;ANY /productmaster/{proxy+}" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="500" y="140" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- Container Layer -->
        <mxCell id="container-layer" value="Container Layer (AWS ECS Fargate)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="280" width="600" height="40" as="geometry" />
        </mxCell>
        
        <!-- ECS Cluster -->
        <mxCell id="ecs-cluster" value="ECS Cluster&#xa;fe-product-master-cluster" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="100" y="340" width="560" height="40" as="geometry" />
        </mxCell>
        
        <!-- ECS Services -->
        <mxCell id="ecs-service-1" value="ECS Service Instance 1&#xa;Nginx + React Build&#xa;Port: 8080&#xa;CPU: 256, Memory: 512MB" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="120" y="400" width="180" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="ecs-service-2" value="ECS Service Instance 2&#xa;Nginx + React Build&#xa;Port: 8080&#xa;CPU: 256, Memory: 512MB" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="320" y="400" width="180" height="80" as="geometry" />
        </mxCell>
        
        <!-- ECR -->
        <mxCell id="ecr" value="Amazon ECR&#xa;fe-product-master&#xa;Docker Images" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="520" y="400" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- Backend Services -->
        <mxCell id="backend-layer" value="Backend Services" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="520" width="600" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="graphql-backend" value="GraphQL Backend&#xa;https://application.ecomthreesixty.com&#xa;/api/productmaster/graphql" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="100" y="580" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="cognito" value="AWS Cognito&#xa;User Authentication&#xa;User Pool ID & Client ID" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="320" y="580" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="secrets-manager" value="AWS Secrets Manager&#xa;- GitHub Token&#xa;- Cognito Config&#xa;- Environment Secrets" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="500" y="580" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- Technology Stack -->
        <mxCell id="tech-stack" value="Technology Stack" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="720" y="80" width="400" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="frontend-tech" value="Frontend Technologies&#xa;• React 19 + React Router v7&#xa;• TypeScript + Vite&#xa;• Material-UI (MUI)&#xa;• SWR + GraphQL&#xa;• React Hook Form + Yup&#xa;• AWS Amplify UI" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;align=left;" vertex="1" parent="1">
          <mxGeometry x="740" y="140" width="180" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="build-tech" value="Build & Deploy&#xa;• pnpm package manager&#xa;• Docker multi-stage build&#xa;• Nginx web server&#xa;• GitHub Actions CI/CD&#xa;• CloudFormation IaC&#xa;• AWS ECS Fargate" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;align=left;" vertex="1" parent="1">
          <mxGeometry x="940" y="140" width="180" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="config-files" value="Key Configuration Files&#xa;• package.json - Dependencies&#xa;• ci/Dockerfile - Container build&#xa;• ci/nginx.conf - Web server config&#xa;• ci/aws/*.yaml - Infrastructure&#xa;• vite.config.ts - Build config&#xa;• tsconfig.json - TypeScript config" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;align=left;" vertex="1" parent="1">
          <mxGeometry x="740" y="280" width="380" height="120" as="geometry" />
        </mxCell>
        
        <!-- Arrows -->
        <mxCell id="arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="browser" target="alb">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="alb" target="api-gateway">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="alb" target="ecs-service-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="alb" target="ecs-service-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="ecs-service-1" target="graphql-backend">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="ecs-service-2" target="cognito">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="ecr" target="ecs-service-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Labels -->
        <mxCell id="label1" value="HTTPS" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="210" y="150" width="60" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="label2" value="Load Balance" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="280" y="250" width="80" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="label3" value="Docker Pull" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="440" y="420" width="70" height="30" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

# Architecture Diagrams

This directory contains comprehensive architecture diagrams for the Ecom360 Product Master Frontend application. These diagrams provide visual representations of the system architecture, deployment pipeline, and AWS infrastructure components.

## Diagram Files

### 1. `architecture-overview.drawio`
**High-level System Architecture**

This diagram shows the complete system architecture from user interaction to backend services, including:

- **User Layer**: Web browser with React SPA
- **Load Balancing**: Application Load Balancer with path-based routing (`/productmaster*`)
- **API Gateway**: AWS API Gateway v2 with route configuration
- **Container Platform**: AWS ECS Fargate with 2 service instances
- **Backend Integration**: GraphQL backend, AWS Cognito, and Secrets Manager
- **Technology Stack**: Complete frontend and build technology overview

**Key Components Illustrated:**
- Request flow from browser through ALB to ECS containers
- Container specifications (CPU: 256, Memory: 512MB)
- ECR integration for Docker image storage
- External service dependencies (GraphQL API, Cognito)

### 2. `deployment-pipeline.drawio`
**CI/CD Deployment Process**

This diagram details the complete deployment pipeline from source code to running application:

- **Source Control**: GitHub repository with trigger events
- **GitHub Actions**: Workflow stages (lint, deploy)
- **Build Process**: Multi-stage Docker build with Node.js and Nginx
- **Deployment Scripts**: Sequential execution of CloudFormation deployments
- **Configuration Management**: Environment variable injection and secrets handling

**Pipeline Stages:**
1. **Lint Stage**: CloudFormation template validation
2. **Deploy Stage**: Infrastructure and application deployment
3. **Build & Push**: Docker image creation and ECR push
4. **Monitoring**: Health checks and deployment verification

**File References:**
- `.github/workflows/deploy-infrastructure.yaml` - Main workflow
- `ci/scripts/cloudformation/*.sh` - Deployment scripts
- `ci/Dockerfile` - Container build configuration
- `ci/aws/*.yaml` - Infrastructure templates

### 3. `aws-infrastructure.drawio`
**Detailed AWS Infrastructure Components**

This diagram provides an in-depth view of all AWS resources and their relationships:

- **Networking**: VPC, subnets, security groups, and routing
- **Compute**: ECS cluster, services, tasks, and container definitions
- **Load Balancing**: ALB configuration, target groups, and listener rules
- **Storage**: ECR repositories with lifecycle policies
- **Security**: IAM roles, policies, and Secrets Manager integration
- **Monitoring**: CloudWatch Logs and Container Insights

**Infrastructure Layers:**
- **Public Subnets**: Internet Gateway, ALB, API Gateway
- **Private Subnets**: ECS tasks, secure container execution
- **IAM Roles**: Execution role, task role, GitHub deploy role
- **External Services**: Cognito authentication, GraphQL backend

## How to Use These Diagrams

### Opening the Diagrams
These `.drawio` files can be opened with:
- **draw.io** (https://app.diagrams.net/) - Free online editor
- **VS Code** with the Draw.io Integration extension
- **Desktop app** - Available for Windows, macOS, and Linux

### Diagram Navigation
Each diagram includes:
- **Color-coded components** for easy identification
- **Detailed labels** with configuration references
- **Flow arrows** showing data/request paths
- **File path references** to actual configuration files

### Understanding the Architecture

#### Request Flow (architecture-overview.drawio)
1. User accesses application via web browser
2. HTTPS request hits Application Load Balancer
3. ALB routes `/productmaster*` requests to ECS containers
4. Nginx serves React SPA with proper routing
5. Frontend communicates with GraphQL backend and Cognito

#### Deployment Flow (deployment-pipeline.drawio)
1. Code push to main branch triggers GitHub Actions
2. CloudFormation templates are validated (lint stage)
3. AWS credentials are configured via OIDC
4. ECR repository is deployed/verified
5. Docker image is built with secrets injection
6. Application infrastructure is deployed via CloudFormation
7. ECS service performs rolling deployment

#### Infrastructure Components (aws-infrastructure.drawio)
- **Shared Infrastructure**: VPC, ALB, API Gateway (imported via CloudFormation)
- **Application-Specific**: ECS cluster, services, target groups, security groups
- **Supporting Services**: ECR, Secrets Manager, CloudWatch Logs
- **External Dependencies**: Cognito, GraphQL backend

## Configuration File Mapping

The diagrams reference these key configuration files:

### CI/CD Configuration
- `.github/workflows/deploy-infrastructure.yaml` - GitHub Actions workflow
- `ci/scripts/cloudformation/deploy-ecr.sh` - ECR deployment script
- `ci/scripts/cloudformation/deploy-fe-product-master.sh` - Application deployment
- `ci/scripts/build-and-push.sh` - Docker build and push script

### Infrastructure as Code
- `ci/aws/ecr.yaml` - ECR repository CloudFormation template
- `ci/aws/fe-product-master.yaml` - Main application infrastructure
- `ci/Dockerfile` - Multi-stage container build
- `ci/nginx.conf` - Web server configuration

### Application Configuration
- `package.json` - Dependencies and build scripts
- `vite.config.ts` - Build tool configuration
- `tsconfig.json` - TypeScript configuration
- `react-router.config.ts` - Routing configuration

## Architecture Principles

### High Availability
- Multiple ECS instances across availability zones
- Application Load Balancer with health checks
- Circuit breaker pattern for deployment safety

### Security
- Private subnets for container execution
- IAM roles with least-privilege access
- Secrets Manager for sensitive configuration
- Security groups with restricted access

### Scalability
- Fargate serverless containers
- Auto-scaling capabilities
- CDN-ready static asset serving

### Maintainability
- Infrastructure as Code (CloudFormation)
- Automated CI/CD pipeline
- Comprehensive monitoring and logging

## Updating the Diagrams

When making infrastructure or deployment changes:

1. **Update the relevant diagram** to reflect new components or flows
2. **Verify file path references** are still accurate
3. **Update this README** if new diagrams are added
4. **Export PNG/SVG versions** if needed for documentation

These diagrams serve as living documentation and should be kept in sync with the actual implementation.

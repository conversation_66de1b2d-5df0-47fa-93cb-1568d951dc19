<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-27T10:00:00.000Z" agent="5.0" etag="ghi789" version="24.7.17">
  <diagram name="AWS Infrastructure" id="aws-infrastructure">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Title -->
        <mxCell id="title" value="AWS Infrastructure - Detailed Component View" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="20" width="569" height="30" as="geometry" />
        </mxCell>
        
        <!-- VPC -->
        <mxCell id="vpc" value="VPC (Shared Infrastructure)&#xa;Import: ecom360-production-vpc-id" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="80" width="1000" height="40" as="geometry" />
        </mxCell>
        
        <!-- Public Subnet -->
        <mxCell id="public-subnet" value="Public Subnets" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="100" y="140" width="460" height="40" as="geometry" />
        </mxCell>
        
        <!-- Private Subnet -->
        <mxCell id="private-subnet" value="Private Subnets" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="580" y="140" width="480" height="40" as="geometry" />
        </mxCell>
        
        <!-- Internet Gateway -->
        <mxCell id="igw" value="Internet Gateway" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="120" y="200" width="120" height="40" as="geometry" />
        </mxCell>
        
        <!-- Application Load Balancer -->
        <mxCell id="alb-detail" value="Application Load Balancer&#xa;Import: ecom360-production-alb-*&#xa;• Listener on port 443 (HTTPS)&#xa;• SSL/TLS termination&#xa;• Path-based routing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="260" y="200" width="180" height="100" as="geometry" />
        </mxCell>
        
        <!-- API Gateway -->
        <mxCell id="api-gw-detail" value="API Gateway v2&#xa;Import: ecom360-production-api-*&#xa;Routes:&#xa;• GET /productmaster&#xa;• ANY /productmaster/{proxy+}&#xa;Integration with ALB" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="460" y="200" width="180" height="100" as="geometry" />
        </mxCell>
        
        <!-- ECS Cluster -->
        <mxCell id="ecs-cluster-detail" value="ECS Cluster: fe-product-master-cluster&#xa;• Fargate capacity providers&#xa;• Container Insights enabled&#xa;• Mixed Fargate/Fargate Spot (1:4 ratio)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="600" y="200" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- Security Groups -->
        <mxCell id="sg-alb" value="ALB Security Group&#xa;Import: ecom360-production-alb-sg-id&#xa;• Inbound: 443 from Internet&#xa;• Outbound: 8080 to ECS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="820" y="200" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="sg-ecs" value="ECS Security Group&#xa;fe-product-master-ecs-sg&#xa;• Inbound: 8080 from ALB SG&#xa;• Outbound: All traffic" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="1000" y="200" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- ECS Service Detail -->
        <mxCell id="ecs-service-detail" value="ECS Service: fe-product-master-service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="600" y="320" width="460" height="40" as="geometry" />
        </mxCell>
        
        <!-- Task Definition -->
        <mxCell id="task-def" value="Task Definition&#xa;Family: fe-product-master-task&#xa;• CPU: 256 units&#xa;• Memory: 512 MB&#xa;• Network Mode: awsvpc&#xa;• Requires: Fargate" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="620" y="380" width="180" height="100" as="geometry" />
        </mxCell>
        
        <!-- Container Definition -->
        <mxCell id="container-def" value="Container: fe-product-master&#xa;• Image: ECR URI + tag&#xa;• Port: 8080&#xa;• Health check: curl /health&#xa;• Environment: NGINX_ENV&#xa;• Secrets: Cognito config" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="820" y="380" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- Target Group -->
        <mxCell id="target-group" value="ALB Target Group&#xa;fe-product-master-tg&#xa;• Port: 8080&#xa;• Protocol: HTTP&#xa;• Health check: /health&#xa;• Target type: IP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="120" y="380" width="160" height="100" as="geometry" />
        </mxCell>
        
        <!-- Listener Rule -->
        <mxCell id="listener-rule" value="Listener Rule&#xa;Priority: 140&#xa;Condition: path-pattern&#xa;Pattern: /productmaster*&#xa;Action: Forward to TG" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="300" y="380" width="160" height="100" as="geometry" />
        </mxCell>
        
        <!-- Service Configuration -->
        <mxCell id="service-config" value="Service Configuration&#xa;• Desired count: 2&#xa;• Launch type: Fargate&#xa;• Platform version: LATEST&#xa;• Deployment: Rolling&#xa;• Circuit breaker enabled" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="480" y="380" width="180" height="100" as="geometry" />
        </mxCell>
        
        <!-- IAM Roles -->
        <mxCell id="iam-section" value="IAM Roles &amp; Permissions" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="520" width="1080" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="execution-role" value="ECS Execution Role&#xa;fe-product-master-ecs-execution-role&#xa;Policies:&#xa;• AmazonECSTaskExecutionRolePolicy&#xa;• ECR access (pull images)&#xa;• Secrets Manager access" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;align=left;" vertex="1" parent="1">
          <mxGeometry x="100" y="580" width="240" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="task-role" value="ECS Task Role&#xa;fe-product-master-ecs-task-role&#xa;Policies:&#xa;• CloudWatch Logs access&#xa;• Application-specific permissions" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;align=left;" vertex="1" parent="1">
          <mxGeometry x="360" y="580" width="240" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="github-role" value="GitHub Deploy Role&#xa;GitHubDeployRole&#xa;Used by GitHub Actions&#xa;• CloudFormation permissions&#xa;• ECS deployment permissions&#xa;• ECR push permissions" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;align=left;" vertex="1" parent="1">
          <mxGeometry x="620" y="580" width="240" height="100" as="geometry" />
        </mxCell>
        
        <!-- Storage & Secrets -->
        <mxCell id="storage-section" value="Storage &amp; Configuration" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="720" width="1080" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="ecr-detail" value="Amazon ECR Repository&#xa;Name: fe-product-master&#xa;Features:&#xa;• Image scanning enabled&#xa;• Lifecycle policy (keep 10 tagged)&#xa;• AES256 encryption" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;align=left;" vertex="1" parent="1">
          <mxGeometry x="100" y="780" width="200" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="secrets-detail" value="AWS Secrets Manager&#xa;Secrets:&#xa;• ecom360/github-token&#xa;• ecom360/cognito_user_pool_id&#xa;• ecom360/cognito_user_pool_client_id&#xa;Region: eu-central-1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;align=left;" vertex="1" parent="1">
          <mxGeometry x="320" y="780" width="220" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="cloudwatch" value="CloudWatch Logs&#xa;Log Group: /ecs/fe-product-master&#xa;• Retention: 30 days&#xa;• Stream prefix: ecs&#xa;• Container logs aggregation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;align=left;" vertex="1" parent="1">
          <mxGeometry x="560" y="780" width="200" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="cognito-detail" value="AWS Cognito (External)&#xa;User Pool Authentication&#xa;• User Pool ID (from secrets)&#xa;• Client ID (from secrets)&#xa;• Integrated with frontend" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;align=left;" vertex="1" parent="1">
          <mxGeometry x="780" y="780" width="180" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="graphql-backend" value="GraphQL Backend (External)&#xa;URL: https://application.ecomthreesixty.com&#xa;Endpoint: /api/productmaster/graphql&#xa;• Product data API&#xa;• Authentication required" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;align=left;" vertex="1" parent="1">
          <mxGeometry x="980" y="780" width="180" height="100" as="geometry" />
        </mxCell>
        
        <!-- Network Flow Arrows -->
        <mxCell id="arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="igw" target="alb-detail">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="alb-detail" target="api-gw-detail">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="alb-detail" target="target-group">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="target-group" target="listener-rule">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="ecs-cluster-detail" target="task-def">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="task-def" target="container-def">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Labels -->
        <mxCell id="label1" value="HTTPS Traffic" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="240" y="210" width="80" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="label2" value="Health Checks" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="200" y="320" width="80" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="label3" value="Container Orchestration" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="700" y="320" width="120" height="30" as="geometry" />
        </mxCell>
        
        <!-- CloudFormation Reference -->
        <mxCell id="cf-ref" value="CloudFormation Templates Reference:&#xa;• ci/aws/fe-product-master.yaml - Main application infrastructure&#xa;• ci/aws/ecr.yaml - ECR repository setup&#xa;• Shared infrastructure imported via cross-stack references" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;align=left;" vertex="1" parent="1">
          <mxGeometry x="880" y="580" width="280" height="100" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

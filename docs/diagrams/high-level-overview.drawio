<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-27T10:00:00.000Z" agent="5.0" etag="xyz123" version="24.7.17">
  <diagram name="High Level Overview" id="high-level-overview">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Title -->
        <mxCell id="title" value="Ecom360 Product Master Frontend - High Level Architecture" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="250" y="30" width="600" height="30" as="geometry" />
        </mxCell>
        
        <!-- User -->
        <mxCell id="user" value="👤 Users" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="100" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- Internet -->
        <mxCell id="internet" value="🌐 Internet" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="280" y="100" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- Load Balancer -->
        <mxCell id="alb" value="⚖️ Application Load Balancer&#xa;(AWS ALB)&#xa;Route: /productmaster*" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="460" y="100" width="180" height="70" as="geometry" />
        </mxCell>
        
        <!-- Container Platform -->
        <mxCell id="ecs" value="🐳 Container Platform&#xa;(AWS ECS Fargate)&#xa;2 instances&#xa;React + Nginx" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="720" y="100" width="180" height="70" as="geometry" />
        </mxCell>
        
        <!-- Source Code -->
        <mxCell id="github" value="📁 Source Code&#xa;(GitHub Repository)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="100" y="220" width="150" height="60" as="geometry" />
        </mxCell>
        
        <!-- CI/CD Pipeline -->
        <mxCell id="cicd" value="🔄 CI/CD Pipeline&#xa;(GitHub Actions)&#xa;Build → Test → Deploy" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="300" y="220" width="180" height="60" as="geometry" />
        </mxCell>
        
        <!-- Container Registry -->
        <mxCell id="ecr" value="📦 Container Registry&#xa;(AWS ECR)&#xa;Docker Images" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="530" y="220" width="150" height="60" as="geometry" />
        </mxCell>
        
        <!-- Infrastructure -->
        <mxCell id="cloudformation" value="🏗️ Infrastructure&#xa;(AWS CloudFormation)&#xa;Infrastructure as Code" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="730" y="220" width="170" height="60" as="geometry" />
        </mxCell>
        
        <!-- Backend Services -->
        <mxCell id="backend-title" value="Backend Services" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="320" width="200" height="30" as="geometry" />
        </mxCell>
        
        <!-- GraphQL API -->
        <mxCell id="graphql" value="🔗 GraphQL API&#xa;Product Data Backend&#xa;ecomthreesixty.com" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="300" y="360" width="160" height="70" as="geometry" />
        </mxCell>
        
        <!-- Authentication -->
        <mxCell id="cognito" value="🔐 Authentication&#xa;(AWS Cognito)&#xa;User Management" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="500" y="360" width="160" height="70" as="geometry" />
        </mxCell>
        
        <!-- Configuration -->
        <mxCell id="secrets" value="⚙️ Configuration&#xa;(AWS Secrets Manager)&#xa;Environment Variables" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="700" y="360" width="160" height="70" as="geometry" />
        </mxCell>
        
        <!-- Technology Stack Box -->
        <mxCell id="tech-box" value="Technology Stack" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="480" width="800" height="30" as="geometry" />
        </mxCell>
        
        <!-- Frontend Tech -->
        <mxCell id="frontend-tech" value="Frontend&#xa;• React 19&#xa;• TypeScript&#xa;• Material-UI&#xa;• Vite" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;align=left;" vertex="1" parent="1">
          <mxGeometry x="120" y="530" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- Build Tech -->
        <mxCell id="build-tech" value="Build &amp; Deploy&#xa;• pnpm&#xa;• Docker&#xa;• Nginx&#xa;• GitHub Actions" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;align=left;" vertex="1" parent="1">
          <mxGeometry x="260" y="530" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- Cloud Tech -->
        <mxCell id="cloud-tech" value="Cloud Platform&#xa;• AWS ECS&#xa;• AWS ALB&#xa;• AWS ECR&#xa;• CloudFormation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;align=left;" vertex="1" parent="1">
          <mxGeometry x="400" y="530" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- Data Tech -->
        <mxCell id="data-tech" value="Data &amp; Auth&#xa;• GraphQL&#xa;• AWS Cognito&#xa;• SWR Caching&#xa;• React Hook Form" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11;align=left;" vertex="1" parent="1">
          <mxGeometry x="540" y="530" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- Monitoring Tech -->
        <mxCell id="monitor-tech" value="Monitoring&#xa;• CloudWatch&#xa;• Health Checks&#xa;• Container Insights&#xa;• Auto Scaling" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11;align=left;" vertex="1" parent="1">
          <mxGeometry x="680" y="530" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- Main Flow Arrows -->
        <mxCell id="arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#666666;" edge="1" parent="1" source="user" target="internet">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#666666;" edge="1" parent="1" source="internet" target="alb">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#666666;" edge="1" parent="1" source="alb" target="ecs">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- CI/CD Flow Arrows -->
        <mxCell id="arrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#d79b00;" edge="1" parent="1" source="github" target="cicd">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#d79b00;" edge="1" parent="1" source="cicd" target="ecr">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#d79b00;" edge="1" parent="1" source="ecr" target="cloudformation">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Container to ECR -->
        <mxCell id="arrow7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#6c8ebf;" edge="1" parent="1" source="ecs" target="ecr">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Backend Service Connections -->
        <mxCell id="arrow8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1;strokeColor=#82b366;" edge="1" parent="1" source="ecs" target="graphql">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1;strokeColor=#d6b656;" edge="1" parent="1" source="ecs" target="cognito">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1;strokeColor=#d79b00;" edge="1" parent="1" source="ecs" target="secrets">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Labels -->
        <mxCell id="label1" value="HTTPS" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="210" y="110" width="40" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label2" value="Load Balance" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="390" y="110" width="60" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label3" value="Container Orchestration" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="650" y="110" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label4" value="Code Push" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;color=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="260" y="230" width="50" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label5" value="Build & Push" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;color=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="490" y="230" width="60" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label6" value="Deploy" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;color=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="690" y="230" width="40" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label7" value="Pull Images" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;color=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="720" y="190" width="60" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label8" value="API Calls" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;color=#82b366;" vertex="1" parent="1">
          <mxGeometry x="720" y="280" width="50" height="20" as="geometry" />
        </mxCell>
        
        <!-- Key Information Box -->
        <mxCell id="key-info" value="🔑 Key Information&#xa;• Route: /productmaster*&#xa;• Port: 8080 (containers)&#xa;• Region: eu-central-1&#xa;• Environment: production" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;align=left;" vertex="1" parent="1">
          <mxGeometry x="100" y="640" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- Deployment Flow -->
        <mxCell id="deploy-flow" value="🚀 Deployment Flow&#xa;1. Code push to main branch&#xa;2. GitHub Actions triggered&#xa;3. Docker image built &amp; pushed&#xa;4. Infrastructure updated&#xa;5. Rolling deployment" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11;align=left;" vertex="1" parent="1">
          <mxGeometry x="320" y="640" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- Scaling Info -->
        <mxCell id="scaling-info" value="📈 Scaling &amp; Reliability&#xa;• 2 container instances&#xa;• Auto-scaling enabled&#xa;• Health checks&#xa;• Circuit breaker protection&#xa;• Multi-AZ deployment" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;align=left;" vertex="1" parent="1">
          <mxGeometry x="540" y="640" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- File References -->
        <mxCell id="file-refs" value="📄 Key Files&#xa;• ci/Dockerfile&#xa;• ci/nginx.conf&#xa;• ci/aws/*.yaml&#xa;• .github/workflows/*.yaml" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;align=left;" vertex="1" parent="1">
          <mxGeometry x="760" y="640" width="140" height="80" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

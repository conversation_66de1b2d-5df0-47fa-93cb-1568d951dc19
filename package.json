{"name": "ecom360-landing-page", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "lint": "eslint --fix", "typecheck": "react-router typegen && tsc", "codegen": "graphql-codegen --require dotenv/config --config codegen.ts --verbose"}, "dependencies": {"@aws-amplify/ui-react": "^6.9.3", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.1.1", "@graphql-codegen/typescript-resolvers": "^4.4.4", "@hookform/resolvers": "^4.1.3", "@mui/icons-material": "^6.4.3", "@mui/lab": "7.0.0-beta.11", "@mui/material": "^6.4.3", "@mui/x-data-grid": "^8.0.0", "@react-router/fs-routes": "^7.1.5", "@react-router/node": "^7.1.5", "@react-router/serve": "^7.1.5", "@sw-ecom360/component-library": "1.0.77", "aws-amplify": "^6.13.2", "dayjs": "^1.11.13", "graphql": "^16.10.0", "graphql-request": "^5.2.0", "isbot": "^5.1.17", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.2", "react-router": "^7.1.5", "swr": "^2.3.3", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.20.0", "@graphql-codegen/cli": "^5.0.5", "@react-router/dev": "^7.1.5", "@sw-ecom360/lint-config": "1.1.13", "@types/node": "^20", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "eslint": "^9.20.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^15.14.0", "prettier": "^3.5.0", "prettier-eslint": "^16.3.0", "react-router-devtools": "^1.1.0", "sheetjs": "^2.0.0", "typescript": "^5.8.2", "typescript-eslint": "^8.23.0", "vite": "^5.4.11", "vite-tsconfig-paths": "^5.1.4"}}
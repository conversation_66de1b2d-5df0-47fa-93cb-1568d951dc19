AWSTemplateFormatVersion: "2010-09-09"
Description: "Ecom360 Product Master SPA application infrastructure"

Parameters:
  ProjectName:
    Type: String
    Default: fe-product-master
    Description: Name of this specific application

  ProjectPrefix:
    Type: String
    Default: ecom360
    Description: Prefix for shared resources (must match networking stack)

  Environment:
    Type: String
    Default: production
    AllowedValues: [development, staging, production]
    Description: Environment name (must match networking stack)

  ImageTag:
    Type: String
    Default: latest
    Description: Docker image tag to deploy

  DesiredCount:
    Type: Number
    Default: 2
    Description: Desired number of ECS tasks

  ContainerCpu:
    Type: Number
    Default: 256
    Description: CPU units for the container (256, 512, 1024, 2048, 4096)

  ContainerMemory:
    Type: Number
    Default: 512
    Description: Memory for the container in MB

Resources:
  # Application-specific Security Group
  ECSSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub ${ProjectName}-ecs-sg
      GroupDescription: Security group for Product Master ECS tasks
      VpcId:
        Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-vpc-id
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 8080
          ToPort: 8080
          SourceSecurityGroupId:
            Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-alb-sg-id
          Description: HTTP access from ALB
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
          Description: All outbound traffic
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-ecs-sg
        - Key: Environment
          Value: !Ref Environment

  # CloudWatch Log Group
  LogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /ecs/${ProjectName}
      RetentionInDays: 30
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-logs
        - Key: Environment
          Value: !Ref Environment

  # IAM Roles
  ECSTaskExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${ProjectName}-ecs-execution-role
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy
      Policies:
        - PolicyName: ECRAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - ecr:GetAuthorizationToken
                  - ecr:BatchCheckLayerAvailability
                  - ecr:GetDownloadUrlForLayer
                  - ecr:BatchGetImage
                Resource: "*"
        - PolicyName: SecretsManagerAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                Resource:
                  - !Sub "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:ecom360/github-token*"
                  - !Sub "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:ecom360/cognito_user_pool_client_id-9F8xv5"
                  - !Sub "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:ecom360/cognito_user_pool_id-hcZMa8"
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-ecs-execution-role
        - Key: Environment
          Value: !Ref Environment

  ECSTaskRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${ProjectName}-ecs-task-role
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: CloudWatchLogs
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource: !GetAtt LogGroup.Arn
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-ecs-task-role
        - Key: Environment
          Value: !Ref Environment

  # ECS Cluster
  ECSCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Sub ${ProjectName}-cluster
      CapacityProviders:
        - FARGATE
        - FARGATE_SPOT
      DefaultCapacityProviderStrategy:
        - CapacityProvider: FARGATE
          Weight: 1
        - CapacityProvider: FARGATE_SPOT
          Weight: 4
      ClusterSettings:
        - Name: containerInsights
          Value: enabled
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-cluster
        - Key: Environment
          Value: !Ref Environment

  # ALB Target Group (app-specific)
  ALBTargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Sub ${ProjectName}-tg
      Port: 8080
      Protocol: HTTP
      VpcId:
        Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-vpc-id
      TargetType: ip
      HealthCheckEnabled: true
      HealthCheckPath: /health
      HealthCheckProtocol: HTTP
      HealthCheckPort: 8080
      HealthCheckIntervalSeconds: 30
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 2
      UnhealthyThresholdCount: 3
      Matcher:
        HttpCode: "200"
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-tg
        - Key: Environment
          Value: !Ref Environment

  # ALB Listener Rule for /productmaster route
  # Manual Priority Assignment:
  ALBListenerRuleProductMaster:
    Type: AWS::ElasticLoadBalancingV2::ListenerRule
    Properties:
      ListenerArn:
        Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-alb-listener-arn
      Priority: 140
      Conditions:
        - Field: path-pattern
          Values:
            - "/productmaster*"
      Actions:
        - Type: forward
          TargetGroupArn: !Ref ALBTargetGroup

  # ECS Task Definition
  ECSTaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub ${ProjectName}-task
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      Cpu: !Ref ContainerCpu
      Memory: !Ref ContainerMemory
      ExecutionRoleArn: !GetAtt ECSTaskExecutionRole.Arn
      TaskRoleArn: !GetAtt ECSTaskRole.Arn
      ContainerDefinitions:
        - Name: !Ref ProjectName
          Image: !Sub
            - "${ECRRepositoryURI}:${ImageTag}"
            - ECRRepositoryURI:
                Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-${ProjectName}-ecr-uri
          PortMappings:
            - ContainerPort: 8080
              Protocol: tcp
          Essential: true
          Environment:
            - Name: NGINX_ENV
              Value: !Ref Environment
          Secrets:
            - Name: VITE_COGNITO_USER_POOL_CLIENT_ID
              ValueFrom: arn:aws:secretsmanager:eu-central-1:586794460318:secret:ecom360/cognito_user_pool_client_id-9F8xv5
            - Name: VITE_COGNITO_USER_POOL_ID
              ValueFrom: arn:aws:secretsmanager:eu-central-1:586794460318:secret:ecom360/cognito_user_pool_id-hcZMa8
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref LogGroup
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: ecs
          HealthCheck:
            Command:
              - CMD-SHELL
              - "curl -f http://localhost:8080/health || exit 1"
            Interval: 30
            Timeout: 5
            Retries: 3
            StartPeriod: 30
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-task
        - Key: Environment
          Value: !Ref Environment

  # ECS Service
  ECSService:
    Type: AWS::ECS::Service
    DependsOn: ALBListenerRuleProductMaster
    Properties:
      ServiceName: !Sub ${ProjectName}-service
      Cluster: !Ref ECSCluster
      TaskDefinition: !Ref ECSTaskDefinition
      DesiredCount: !Ref DesiredCount
      LaunchType: FARGATE
      PlatformVersion: LATEST
      NetworkConfiguration:
        AwsvpcConfiguration:
          Subnets:
            - Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-private-subnet-1-id
            - Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-private-subnet-2-id
          SecurityGroups:
            - !Ref ECSSecurityGroup
          AssignPublicIp: DISABLED
      LoadBalancers:
        - TargetGroupArn: !Ref ALBTargetGroup
          ContainerName: !Ref ProjectName
          ContainerPort: 8080
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 50
        DeploymentCircuitBreaker:
          Enable: true
          Rollback: true
      EnableExecuteCommand: true
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-service
        - Key: Environment
          Value: !Ref Environment

  # API Gateway Routes for this app
  ApiGatewayRouteProductMaster:
    Type: AWS::ApiGatewayV2::Route
    Properties:
      ApiId:
        Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-api-id
      RouteKey: "GET /productmaster"
      Target: !Sub
        - "integrations/${IntegrationId}"
        - IntegrationId:
            Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-api-integration-id

  ApiGatewayRouteProductMasterProxy:
    Type: AWS::ApiGatewayV2::Route
    Properties:
      ApiId:
        Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-api-id
      RouteKey: "ANY /productmaster/{proxy+}"
      Target: !Sub
        - "integrations/${IntegrationId}"
        - IntegrationId:
            Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-api-integration-id

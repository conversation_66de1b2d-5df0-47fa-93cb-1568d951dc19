#!/bin/bash

# Deploy Product Master application
# Usage: ./ci/scripts/cloudformation/deploy-product-master.sh [AWS_REGION] [ENVIRONMENT] [IMAGE_TAG]

set -e

# Default values
AWS_REGION=${1:-eu-central-1}
ENVIRONMENT=${2:-production}
IMAGE_TAG=${3:-latest}
PROJECT_NAME="fe-product-master"
PROJECT_PREFIX="ecom360"
APP_STACK_NAME="${PROJECT_NAME}-${ENVIRONMENT}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color
NO_EXECUTE=""

if [[ "$*" == *"--no-execute-changeset"* ]]; then
    NO_EXECUTE="--no-execute-changeset"
fi

echo -e "${BLUE}🚀 Starting application deployment for ${PROJECT_NAME}${NC}"
echo -e "${BLUE}📋 Configuration:${NC}"
echo -e "   Region: ${AWS_REGION}"
echo -e "   Environment: ${ENVIRONMENT}"
echo -e "   Image Tag: ${IMAGE_TAG}"
echo -e "   App Stack: ${APP_STACK_NAME}"
echo ""

# Check prerequisites
echo -e "${YELLOW}🔍 Checking prerequisites...${NC}"

if ! command -v aws &> /dev/null; then
    echo -e "${RED}❌ AWS CLI is not installed${NC}"
    exit 1
fi

if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed${NC}"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker is not running${NC}"
    exit 1
fi

# Get AWS account ID
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to get AWS account ID. Check your AWS credentials${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"
echo -e "${GREEN}📋 AWS Account ID: ${AWS_ACCOUNT_ID}${NC}"
echo ""

# Step 1: Check ECR repository exists (should be deployed via ECR stack)
echo -e "${YELLOW}📦 Step 1: Checking ECR repository exists...${NC}"
if ! aws ecr describe-repositories --repository-names ${PROJECT_NAME} --region ${AWS_REGION} &> /dev/null; then
    echo -e "${RED}❌ ECR repository ${PROJECT_NAME} does not exist${NC}"
    echo -e "${YELLOW}💡 Deploy ECR stack first: ./scripts/cloudformation/deploy-ecr.sh ${AWS_REGION} ${ENVIRONMENT}${NC}"
    exit 1
fi

echo -e "${GREEN}✅ ECR repository exists${NC}"

# Step 2: Build and push Docker image

# Step 2: Build and push Docker image
echo -e "${YELLOW}📦 Step 2: Building and pushing Docker image...${NC}"
./ci/scripts/build-and-push.sh ${AWS_REGION} ${PROJECT_NAME} ${IMAGE_TAG} ${NO_EXECUTE}
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to build and push Docker image${NC}"
    exit 1
fi

ECR_URI="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PROJECT_NAME}:${IMAGE_TAG}"
echo -e "${GREEN}✅ Docker image built and pushed successfully${NC}"
echo ""

echo -e "${YELLOW}🏗️  Step 3: Deploying application stack...${NC}"

aws cloudformation deploy \
    --stack-name ${APP_STACK_NAME} \
    --template-file ci/aws/${PROJECT_NAME}.yaml \
    --parameter-overrides \
        ProjectName=${PROJECT_NAME} \
        ProjectPrefix=${PROJECT_PREFIX} \
        Environment=${ENVIRONMENT} \
        ImageTag=${IMAGE_TAG} \
        DesiredCount=1 \
    --capabilities CAPABILITY_NAMED_IAM \
    --region ${AWS_REGION} \
    --tags \
        Project=${PROJECT_NAME} \
        Environment=${ENVIRONMENT} \
        ManagedBy=CloudFormation \
    ${NO_EXECUTE}

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Application stack failed${NC}"
    exit 1
fi

# Skip output retrieval if in preview mode
if [[ -n "$NO_EXECUTE" ]]; then
    echo -e "${GREEN}✅ Changeset created successfully! Review in AWS Console${NC}"
    exit 0
fi

echo -e "${GREEN}✅ Application stack deployed successfully${NC}"
echo ""

#!/bin/bash

# Deploy ECR repositories for Ecom360 applications
# Usage: ./ci/scripts/cloudformation/deploy-ecr.sh [AWS_REGION] [ENVIRONMENT]

set -e

# Default values
AWS_REGION=${1:-eu-central-1}
ENVIRONMENT=${2:-production}
PROJECT_PREFIX="ecom360"
PROJECT_NAME="fe-product-master"
ECR_STACK_NAME="${PROJECT_NAME}-ecr-${ENVIRONMENT}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color
NO_EXECUTE=""

if [[ "$*" == *"--no-execute-changeset"* ]]; then
    NO_EXECUTE="--no-execute-changeset"
fi

echo -e "${BLUE}🏗️  Deploying ECR repositories for Ecom360${NC}"
echo -e "${BLUE}📋 Configuration:${NC}"
echo -e "   Region: ${AWS_REGION}"
echo -e "   Environment: ${ENVIRONMENT}"
echo -e "   Stack Name: ${ECR_STACK_NAME}"
echo ""

# Deploy ECR stack
echo -e "${YELLOW}🏗️  Deploying ECR stack...${NC}"

aws cloudformation deploy \
    --stack-name ${ECR_STACK_NAME} \
    --template-file ci/aws/ecr.yaml \
    --parameter-overrides \
        ProjectPrefix=${PROJECT_PREFIX} \
        Environment=${ENVIRONMENT} \
        ProjectName=${PROJECT_NAME} \
    --region ${AWS_REGION} \
    --tags \
        Project=${PROJECT_PREFIX} \
        Environment=${ENVIRONMENT} \
        Application=${PROJECT_NAME} \
        ManagedBy=CloudFormation \
        Purpose=ECR \
    ${NO_EXECUTE}

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ ECR stack failed${NC}"
    exit 1
fi

# Skip output retrieval if in preview mode
if [[ -n "$NO_EXECUTE" ]]; then
    echo -e "${GREEN}✅ Changeset created successfully! Review in AWS Console${NC}"
    exit 0
fi

echo -e "${GREEN}✅ ECR stack deployed successfully${NC}"

#!/bin/bash

# Build and push Docker image to ECR
# Usage: ./ci/scripts/build-and-push.sh [AWS_REGION] [ECR_REPOSITORY_NAME] [IMAGE_TAG]

set -e

# Default values
AWS_REGION=${1:-eu-central-1}
ECR_REPOSITORY_NAME=${2:-fe-product-master}
IMAGE_TAG=${3:-latest}
VITE_GRAPHQL_BACKEND_URL=https://application.ecomthreesixty.com/api/productmaster/graphql
# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

NO_EXECUTE=""
if [[ "$*" == *"--no-execute-changeset"* ]]; then
    NO_EXECUTE="--no-execute-changeset"
fi

echo -e "${GREEN}🚀 Starting Docker build and push process...${NC}"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo -e "${RED}❌ AWS CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

# Get AWS account ID
echo -e "${YELLOW}📋 Getting AWS account ID...${NC}"
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to get AWS account ID. Please check your AWS credentials.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ AWS Account ID: ${AWS_ACCOUNT_ID}${NC}"

# ECR repository URI
ECR_URI="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY_NAME}"

# Check if ECR repository exists (should be created by ECR stack)
echo -e "${YELLOW}📦 Checking if ECR repository exists...${NC}"
if ! aws ecr describe-repositories --repository-names ${ECR_REPOSITORY_NAME} --region ${AWS_REGION} &> /dev/null; then
    echo -e "${RED}❌ ECR repository ${ECR_REPOSITORY_NAME} does not exist${NC}"
    echo -e "${YELLOW}💡 Deploy ECR stack first: ./ci/scripts/cloudformation/deploy-ecr.sh${NC}"
    exit 1
fi

echo -e "${GREEN}✅ ECR repository exists${NC}"

# Login to ECR
echo -e "${YELLOW}🔐 Logging in to ECR...${NC}"
aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${ECR_URI}
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to login to ECR${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Successfully logged in to ECR${NC}"

# Get GitHub token from AWS Secrets Manager
echo -e "${YELLOW}🔐 Retrieving GitHub token from Secrets Manager...${NC}"
GITHUB_TOKEN=$(aws secretsmanager get-secret-value \
    --secret-id "ecom360/github-token" \
    --region ${AWS_REGION} \
    --query SecretString \
    --output text 2>/dev/null)

if [ $? -ne 0 ] || [ -z "$GITHUB_TOKEN" ]; then
    echo -e "${RED}❌ Failed to retrieve GitHub token from Secrets Manager${NC}"
    echo -e "${YELLOW}💡 Make sure the secret 'ecom360/github-token' exists in AWS Secrets Manager${NC}"
    exit 1
fi

echo -e "${GREEN}✅ GitHub token retrieved successfully${NC}"

# Get Cognito User Pool ID from AWS Secrets Manager
echo -e "${YELLOW}🔐 Retrieving Cognito User Pool ID from Secrets Manager...${NC}"
VITE_COGNITO_USER_POOL_ID=$(aws secretsmanager get-secret-value \
    --secret-id "ecom360/cognito_user_pool_id" \
    --region ${AWS_REGION} \
    --query SecretString \
    --output text 2>/dev/null)

if [ $? -ne 0 ] || [ -z "$VITE_COGNITO_USER_POOL_ID" ]; then
    echo -e "${RED}❌ Failed to retrieve Cognito User Pool ID from Secrets Manager${NC}"
    echo -e "${YELLOW}💡 Make sure the secret 'ecom360/cognito_user_pool_id' exists${NC}"
    exit 1
fi

# Get Cognito User Pool Client ID from AWS Secrets Manager
echo -e "${YELLOW}🔐 Retrieving Cognito User Pool Client ID from Secrets Manager...${NC}"
VITE_COGNITO_USER_POOL_CLIENT_ID=$(aws secretsmanager get-secret-value \
    --secret-id "ecom360/cognito_user_pool_client_id" \
    --region ${AWS_REGION} \
    --query SecretString \
    --output text 2>/dev/null)

if [ $? -ne 0 ] || [ -z "$VITE_COGNITO_USER_POOL_CLIENT_ID" ]; then
    echo -e "${RED}❌ Failed to retrieve Cognito User Pool Client ID from Secrets Manager${NC}"
    echo -e "${YELLOW}💡 Make sure the secret 'ecom360/cognito_user_pool_client_id' exists${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Cognito configuration retrieved successfully${NC}"

# Build Docker image with GitHub token and Cognito configuration
echo -e "${YELLOW}🔨 Building Docker image with GitHub Package Registry and Cognito configuration...${NC}"
docker build \
    --build-arg GITHUB_TOKEN=${GITHUB_TOKEN} \
    --build-arg VITE_COGNITO_USER_POOL_ID=${VITE_COGNITO_USER_POOL_ID} \
    --build-arg VITE_COGNITO_USER_POOL_CLIENT_ID=${VITE_COGNITO_USER_POOL_CLIENT_ID} \
    --build-arg VITE_GRAPHQL_BACKEND_URL=${VITE_GRAPHQL_BACKEND_URL} \
    -f ci/Dockerfile \
    -t ${ECR_REPOSITORY_NAME}:${IMAGE_TAG} .

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to build Docker image${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker image built successfully${NC}"

if [ -z "$NO_EXECUTE" ]; then
    # Tag image for ECR
    echo -e "${YELLOW}🏷️  Tagging image for ECR...${NC}"
    docker tag ${ECR_REPOSITORY_NAME}:${IMAGE_TAG} ${ECR_URI}:${IMAGE_TAG}
    docker tag ${ECR_REPOSITORY_NAME}:${IMAGE_TAG} ${ECR_URI}:latest

    # Push image to ECR
    echo -e "${YELLOW}📤 Pushing image to ECR...${NC}"
    docker push ${ECR_URI}:${IMAGE_TAG}
    docker push ${ECR_URI}:latest

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Successfully pushed image to ECR${NC}"
        echo -e "${GREEN}📍 Image URI: ${ECR_URI}:${IMAGE_TAG}${NC}"
        echo -e "${GREEN}📍 Latest URI: ${ECR_URI}:latest${NC}"
    else
        echo -e "${RED}❌ Failed to push image to ECR${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⏭️  Skipping tagging and pushing (--no-execute-changeset mode)${NC}"
fi

echo -e "${YELLOW}🧹 Cleaning up local images...${NC}"
docker rmi ${ECR_REPOSITORY_NAME}:${IMAGE_TAG} ${ECR_URI}:${IMAGE_TAG} ${ECR_URI}:latest 2>/dev/null || true
echo -e "${GREEN}✅ Local images cleaned up${NC}"

echo -e "${GREEN}🎉 Build and push completed successfully!${NC}"
echo -e "${GREEN}📋 Summary:${NC}"
echo -e "   AWS Region: ${AWS_REGION}"
echo -e "   Repository: ${ECR_REPOSITORY_NAME}"
echo -e "   Image Tag: ${IMAGE_TAG}"
echo -e "   ECR URI: ${ECR_URI}:${IMAGE_TAG}"

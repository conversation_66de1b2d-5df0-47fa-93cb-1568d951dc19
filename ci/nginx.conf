events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    sendfile on;
    keepalive_timeout 65;

    server {
        listen 8080;
        server_name _;
        root /app/build/client;
        index index.html;

        # ALB Health check endpoint (for target group health checks)
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        location = /productmaster {
            return 301 $scheme://$host/productmaster/;
        }

        # Product Master App - all sub-routes (but not assets)
        location /productmaster/ {
            add_header X-Debug-Path $uri always;
            try_files $uri /index.html;
        }

        # Product Master App - static assets
        location /productmaster/assets/ {
            alias /app/build/client/assets/;
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri =404;
        }
    }
}

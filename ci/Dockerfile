# Build stage
FROM node:22-alpine AS builder

RUN corepack enable && corepack prepare pnpm@latest --activate

WORKDIR /app

# Accept build arguments
ARG GITHUB_TOKEN
ARG VITE_COGNITO_USER_POOL_ID
ARG VITE_COGNITO_USER_POOL_CLIENT_ID
ARG VITE_GRAPHQL_BACKEND_URL

# Set environment variables for Vite build
ENV VITE_COGNITO_USER_POOL_ID=${VITE_COGNITO_USER_POOL_ID}
ENV VITE_COGNITO_USER_POOL_CLIENT_ID=${VITE_COGNITO_USER_POOL_CLIENT_ID}
ENV VITE_GRAPHQL_BACKEND_URL=${VITE_GRAPHQL_BACKEND_URL}

# Create .npmrc with GitHub Package Registry configuration
RUN echo "@sw-ecom360:registry=https://npm.pkg.github.com" > .npmrc && \
    echo "//npm.pkg.github.com/:_authToken=${GITHUB_TOKEN}" >> .npmrc

# Copy package files and install dependencies
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

# Copy source code and build
COPY . .
RUN pnpm run build

# Remove .npmrc after installation for security
RUN rm -f .npmrc

# Production stage
FROM nginx:alpine

# Install curl for health checks
RUN apk add --no-cache curl

# Copy nginx configuration
COPY ci/nginx.conf /etc/nginx/nginx.conf

# Copy built application
COPY --from=builder /app/build/client /app/build/client

EXPOSE 8080

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
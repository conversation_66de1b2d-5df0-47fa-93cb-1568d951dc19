import { Box, Skeleton, Paper } from "@mui/material";

const rowCount = 6;
const columns = [
  { width: 200 },
  { width: 250 },
  { width: 120 },
  { width: 180 },
];

export default function OverviewSkeleton() {
  return (
    <Paper sx={{ overflowX: "auto", p: 2 }}>
      <Box mb={2}>
        <Skeleton variant="text" width={160} height={32} />
      </Box>
      <Box display="flex" flexDirection="column" gap={0.5}>
        <Box display="flex" alignItems="center" mb={1}>
          {columns.map((col, idx) => (
            <Skeleton
              key={idx}
              variant="rectangular"
              width={col.width}
              height={28}
              sx={{ mr: idx < columns.length - 1 ? 2 : 0, borderRadius: 1 }}
            />
          ))}
        </Box>
        {Array.from({ length: rowCount }).map((_, rowIdx) => (
          <Box key={rowIdx} display="flex" alignItems="center" mb={0.5}>
            {columns.map((col, idx) => (
              <Skeleton
                key={idx}
                variant="rectangular"
                width={col.width}
                height={24}
                sx={{ mr: idx < columns.length - 1 ? 2 : 0, borderRadius: 1 }}
                animation="wave"
              />
            ))}
          </Box>
        ))}
      </Box>
    </Paper>
  );
}

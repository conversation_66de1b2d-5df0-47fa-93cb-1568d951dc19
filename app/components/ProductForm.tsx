import { Grid2, TextField } from "@mui/material";
import { Controller, FormProvider, useForm } from "react-hook-form";
import type { Product } from "~/generated/graphql";

interface IProductFormProps {
  product?: Product;
  visibleTextFields?: (keyof Product)[];
  disabledTextFields?: (keyof Product)[];
}

export function ProductForm({
  product,
  disabledTextFields,
  visibleTextFields,
}: IProductFormProps) {
  const form = useForm<Product>({
    defaultValues: product,
  });

  const { control } = form;

  const isFieldHidden = (key: keyof Product) => {
    if (!visibleTextFields) {
      return false;
    }
    return !visibleTextFields.includes(key);
  };

  const isFieldDisabled = (key: keyof Product) => {
    if (!disabledTextFields) {
      return false;
    }
    return disabledTextFields.includes(key);
  };

  return (
    <FormProvider {...form}>
      <form>
        <Grid2 container spacing={2} sx={{ mt: 1 }}>
          {!isFieldHidden("id") && (
            <Grid2 size={{ xs: 12, sm: 6, md: 6 }} key="id">
              <Controller
                name="id"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="ID"
                    variant="outlined"
                    disabled={isFieldDisabled("id")}
                  />
                )}
              />
            </Grid2>
          )}
          {!isFieldHidden("productName") && (
            <Grid2 size={{ xs: 12, sm: 6, md: 6 }} key="productName">
              <Controller
                name="productName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Product Name"
                    variant="outlined"
                    disabled={isFieldDisabled("productName")}
                  />
                )}
              />
            </Grid2>
          )}
        </Grid2>
      </form>
    </FormProvider>
  );
}

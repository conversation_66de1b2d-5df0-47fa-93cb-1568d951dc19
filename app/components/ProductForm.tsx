import { Grid2, TextField } from "@mui/material";
import { Controller, FormProvider, useForm } from "react-hook-form";
import type { Product } from "~/generated/graphql";

interface IProductFormProps {
  product?: Product;
  visibleTextFields?: (keyof Product)[];
  disabledTextFields?: (keyof Product)[];
}

export function ProductForm({
  product,
  disabledTextFields,
  visibleTextFields,
}: IProductFormProps) {
  const form = useForm<Product>({
    defaultValues: product,
  });

  const { control } = form;

  const isFieldHidden = (key: keyof Product) => {
    if (!visibleTextFields) {
      return false;
    }
    return !visibleTextFields.includes(key);
  };

  const isFieldDisabled = (key: keyof Product) => {
    if (!disabledTextFields) {
      return false;
    }
    return disabledTextFields.includes(key);
  };

  return (
    <FormProvider {...form}>
      <form>
        <Grid2 container spacing={2} sx={{ mt: 1 }}>
          {!isFieldHidden("title") && (
            <Grid2 size={{ xs: 12, sm: 6, md: 12 }} key="title">
              <Controller
                name="title"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Title"
                    variant="outlined"
                    disabled={isFieldDisabled("title")}
                  />
                )}
              />
            </Grid2>
          )}
          {!isFieldHidden("id") && (
            <Grid2 size={{ xs: 12, sm: 6, md: 6 }} key="id">
              <Controller
                name="id"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="ID"
                    variant="outlined"
                    disabled={isFieldDisabled("id")}
                  />
                )}
              />
            </Grid2>
          )}
          {!isFieldHidden("shop_id") && (
            <Grid2 size={{ xs: 12, sm: 6, md: 6 }} key="shop_id">
              <Controller
                name="shop_id"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Shop ID"
                    variant="outlined"
                    disabled={isFieldDisabled("shop_id")}
                  />
                )}
              />
            </Grid2>
          )}
        </Grid2>
      </form>
    </FormProvider>
  );
}

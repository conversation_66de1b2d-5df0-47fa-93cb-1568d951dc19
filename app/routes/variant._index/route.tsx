import { useGraphQLQuery } from "@sw-ecom360/component-library/gql";
import { Suspense, useState } from "react";
import { DEFAULT_PAGINATED_RESPONSE } from "~/common/constants";
import OverviewSkeleton from "~/components/OverviewSkeleton";
import VariantOverview from "./components/VariantOverview";
import { GET_VARIANTS } from "./graphQL/getVariants";
import type { IVariantsData } from "./interfaces/variants";
import type { GridPaginationModel } from "@mui/x-data-grid";

export default function VariantRoute() {
  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
    page: 0,
    pageSize: 10,
  });
  const [quickSearchQuery, setQuickSearchQuery] = useState<string>("");
  const pagination = {
    page: paginationModel.page + 1,
    pageSize: paginationModel.pageSize,
  };

  // Use search query when searchTerm is provided, otherwise use regular products query
  const variables = quickSearchQuery
    ? { quickSearchQuery, pagination }
    : { pagination };

  const { data, error, isLoading } = useGraphQLQuery<IVariantsData>(
    GET_VARIANTS,
    variables,
  );

  const handleSearch = (term: string) => {
    setQuickSearchQuery(term);
    // Reset to first page when searching
    setPaginationModel((prev) => ({ ...prev, page: 0 }));
  };

  if (error) {
    throw error;
  }

  return (
    <Suspense fallback={<OverviewSkeleton />}>
      <VariantOverview
        isLoading={isLoading}
        variants={data?.variants ?? DEFAULT_PAGINATED_RESPONSE}
        paginationModel={paginationModel}
        onPaginationModelChange={setPaginationModel}
        onSearch={handleSearch}
        searchTerm={quickSearchQuery}
      />
    </Suspense>
  );
}

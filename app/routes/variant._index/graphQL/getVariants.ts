import { gql } from "graphql-request";

export const GET_VARIANTS = gql`
  query GetVariants(
    $pagination: PaginationInput!
    $where: VariantWhereInput
    $searchTerm: String
  ) {
    variants(
      pagination: $pagination
      where: $where
      quickSearchQuery: $searchTerm
    ) {
      data {
        id
        variantName
        sku
        createdAt
      }
      meta {
        totalCount
      }
    }
  }
`;

import { gql } from "graphql-request";

export const GET_VARIANTS = gql`
  query GetVariants($pagination: PaginationInput) {
    variants(pagination: $pagination) {
      items {
        id
        name
        title
        sku
        price
        compare_at
      }
      meta {
        currentPage
        hasNextPage
        hasPreviousPage
        totalCount
        totalPages
      }
    }
  }
`;

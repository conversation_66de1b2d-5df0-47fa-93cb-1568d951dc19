import { gql } from "graphql-request";

export const GET_VARIANTS = gql`
  query GetVariants(
    $pagination: PaginationInput!
    $where: VariantWhereInput
    $quickSearchQuery: String
  ) {
    variants(
      pagination: $pagination
      where: $where
      quickSearchQuery: $quickSearchQuery
    ) {
      data {
        id
        variantName
        sku
        createdAt
      }
      meta {
        totalCount
      }
    }
  }
`;

import { gql } from "graphql-request";

export const SEARCH_VARIANTS = gql`
  query SearchVariants($searchTerm: String!, $pagination: PaginationInput) {
    searchVariants(query: $searchTerm, pagination: $pagination) {
      items {
        id
        name
        sku
        title
        price
        compare_at
      }
      meta {
        currentPage
        hasNextPage
        hasPreviousPage
        totalCount
        totalPages
      }
    }
  }
`;

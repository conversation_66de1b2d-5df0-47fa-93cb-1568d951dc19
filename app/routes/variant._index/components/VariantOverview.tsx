import { Box, CircularProgress, Typography } from "@mui/material";
import {
  DataGrid,
  type GridColDef,
  type GridPaginationModel,
} from "@mui/x-data-grid";
import { PAGE_SIZE_OPTIONS } from "@sw-ecom360/component-library";
import { useStableRowCount } from "@sw-ecom360/component-library/hooks";
import { type PaginatedVariants } from "~/generated/graphql";

interface IProductOverviewProps {
  variants: PaginatedVariants;
  paginationModel: GridPaginationModel;
  isLoading?: boolean;
  onPaginationModelChange: (model: GridPaginationModel) => void;
  onSearch: (searchTerm: string) => void;
  searchTerm?: string;
}

const columns: GridColDef[] = [
  { field: "id", headerName: "ID", flex: 1 },
  { field: "name", headerName: "Name", flex: 1 },
  { field: "title", headerName: "Title", flex: 1 },
  { field: "sku", headerName: "SKU", flex: 1 },
  { field: "price", headerName: "Price", flex: 1 },
  {
    field: "compare_at",
    headerName: "Compare At",
    flex: 1,
  },
];

export default function VariantOverview({
  variants,
  paginationModel,
  searchTerm,
  isLoading,
  onPaginationModelChange,
  onSearch,
}: IProductOverviewProps) {
  const rowCount = useStableRowCount({
    isLoading: !!isLoading,
    actualRowCount: variants?.meta?.totalCount,
  });

  const onFilterModelChange = ({
    quickFilterValues,
  }: {
    quickFilterValues?: string[];
  }) => {
    onSearch(quickFilterValues?.[0] ?? "");
  };

  return (
    <Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Typography variant="h5" component="h2">
          Variants
        </Typography>
      </Box>
      <DataGrid
        loading={!!isLoading}
        filterModel={{
          items: [],
          quickFilterValues: [searchTerm],
        }}
        filterDebounceMs={500}
        onFilterModelChange={onFilterModelChange}
        columns={columns}
        rows={variants.items}
        pagination
        paginationMode="server"
        filterMode="server"
        rowCount={rowCount}
        paginationModel={paginationModel}
        onPaginationModelChange={onPaginationModelChange}
        pageSizeOptions={PAGE_SIZE_OPTIONS}
        disableColumnSelector
        disableColumnFilter
        slots={{
          loadingOverlay: () => (
            <Box p={2} textAlign="center">
              <CircularProgress size={100} />
            </Box>
          ),
        }}
        slotProps={{
          toolbar: {
            showQuickFilter: true,
            csvOptions: { disableToolbarButton: true },
            printOptions: { disableToolbarButton: true },
            quickFilterProps: {
              quickFilterParser: (filterValue: string) => {
                return [filterValue];
              },
            },
          },
        }}
        showToolbar
      />
    </Box>
  );
}

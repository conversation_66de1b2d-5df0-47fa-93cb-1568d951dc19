import {
  DataGrid,
  type GridColDef,
  type GridPaginationModel,
} from "@mui/x-data-grid";
import { PAGE_SIZE_OPTIONS } from "@sw-ecom360/component-library";
import { useGraphQLQuery } from "@sw-ecom360/component-library/gql";
import { EcomError } from "@sw-ecom360/component-library/utils";
import { Suspense, useState } from "react";
import { useParams } from "react-router";
import { GET_PRODUCT_VARIANTS } from "~/routes/product.$id/graphQL/getProductVariants";
import type { Variant } from "~/generated/graphql";
import type { IVariantsData } from "~/routes/product.$id/interfaces/variants";

const variantColumns: GridColDef<Variant>[] = [
  { headerName: "ID", field: "id", flex: 1 },
  { headerName: "SKU", field: "sku", flex: 1 },
  { headerName: "Title", field: "title", flex: 1 },
  { headerName: "Price", field: "price", flex: 1 },
  { headerName: "Compare at", field: "compare_at", flex: 1 },
];

function VariantsContent() {
  const { id } = useParams();
  const productId = Number(id);

  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
    page: 0,
    pageSize: 10,
  });

  const pagination = {
    page: paginationModel.page + 1,
    pageSize: paginationModel.pageSize,
  };

  const { data, error } = useGraphQLQuery<IVariantsData>(GET_PRODUCT_VARIANTS, {
    productId,
    pagination,
  });

  if (error) {
    throw new EcomError(
      "Product Variants Error",
      "Something happened while fetching the product variants",
    );
  }

  return (
    <DataGrid
      columns={variantColumns}
      rows={data?.variants.items}
      pagination
      paginationMode="server"
      rowCount={data?.variants.meta.totalCount}
      paginationModel={paginationModel}
      onPaginationModelChange={setPaginationModel}
      pageSizeOptions={PAGE_SIZE_OPTIONS}
    />
  );
}

export default function Variants() {
  return (
    <Suspense>
      <VariantsContent />
    </Suspense>
  );
}

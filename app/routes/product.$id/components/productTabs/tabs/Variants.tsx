import { DataGrid, type GridColDef } from "@mui/x-data-grid";
import { PAGE_SIZE_OPTIONS } from "@sw-ecom360/component-library";
import { useGraphQLQuery } from "@sw-ecom360/component-library/gql";
import { usePagination } from "@sw-ecom360/component-library/hooks";
import { EcomError, formatDate } from "@sw-ecom360/component-library/utils";
import { useParams } from "react-router";
import { WhereOperator, type Variant } from "~/generated/graphql";
import { GET_VARIANTS } from "~/routes/variant._index/graphQL/getVariants";
import type { IVariantsData } from "~/routes/product.$id/interfaces/variants";

const variantColumns: GridColDef<Variant>[] = [
  { headerName: "ID", field: "id", flex: 1 },
  { headerName: "SKU", field: "sku", flex: 1 },
  { headerName: "Variant Name", field: "variantName", flex: 1 },
  {
    headerName: "Created At",
    field: "createdAt",
    flex: 1,
    valueFormatter: (params) => formatDate(params),
  },
];

export default function Variants() {
  const { id } = useParams();

  const { paginationModel, setPaginationModel } = usePagination();

  const pagination = {
    page: paginationModel.page,
    pageSize: paginationModel.pageSize,
  };
  console.log("id", id, typeof id);

  // Ensure id is defined and convert to string if needed
  if (!id) {
    throw new EcomError(
      "Product ID Missing",
      "Product ID is required to fetch variants",
    );
  }

  const { data, error } = useGraphQLQuery<IVariantsData>(
    GET_VARIANTS,
    {
      where: {
        productId: { operation: WhereOperator.Is, value: String(id) },
      },
      pagination,
    },
    { suspense: true },
  );

  if (error) {
    throw new EcomError(
      "Product Variants Error",
      "Something happened while fetching the product variants",
    );
  }

  return (
    <DataGrid
      columns={variantColumns}
      rows={data?.variants.data}
      pagination
      paginationMode="server"
      rowCount={data?.variants.meta.totalCount}
      paginationModel={paginationModel}
      onPaginationModelChange={setPaginationModel}
      pageSizeOptions={PAGE_SIZE_OPTIONS}
    />
  );
}

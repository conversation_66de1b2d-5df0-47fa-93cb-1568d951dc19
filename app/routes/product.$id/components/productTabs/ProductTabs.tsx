import { TabContext, TabPanel } from "@mui/lab";
import { Tab, Tabs } from "@mui/material";
import React, { Suspense, useState } from "react";
import ProductTabsSkeleton from "./ProductTabsSkeleton";
import Variants from "./tabs/Variants";

export enum TabMetadata {
  Variants = 0,
}

export default function ProductTabs() {
  const [activeTab, setTab] = useState<TabMetadata>(TabMetadata.Variants);

  const handleChange = (event: React.SyntheticEvent, newTab: number) => {
    setTab(newTab);
  };

  return (
    <TabContext value={activeTab}>
      <Tabs value={activeTab} onChange={handleChange}>
        <Tab label="Variants" value={TabMetadata.Variants} />
      </Tabs>

      <TabPanel value={TabMetadata.Variants} sx={{ p: 0, pt: 2 }}>
        <Suspense fallback={<ProductTabsSkeleton />}>
          <Variants />
        </Suspense>
      </TabPanel>
    </TabContext>
  );
}

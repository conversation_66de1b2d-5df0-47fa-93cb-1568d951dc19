import { Box, Typography, Paper } from "@mui/material";
import { ProductForm } from "~/components/ProductForm";
import ProductTabs from "./productTabs/ProductTabs";
import type { Product } from "~/generated/graphql";

interface IProductDetailFormProps {
  product: Product;
}

export default function ProductDetailForm({
  product,
}: IProductDetailFormProps) {
  const editableFields: (keyof Product)[] = ["id", "productName"];

  return (
    <Box p={4}>
      <Paper elevation={3} sx={{ padding: 3, marginBottom: 4 }}>
        <Typography variant="h5" gutterBottom>
          Product Details
        </Typography>
        <ProductForm product={product} disabledTextFields={editableFields} />
      </Paper>
      <ProductTabs />
    </Box>
  );
}

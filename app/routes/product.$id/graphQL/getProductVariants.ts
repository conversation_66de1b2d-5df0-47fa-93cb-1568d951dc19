import { gql } from "graphql-request";

export const GET_PRODUCT_VARIANTS = gql`
  query GetProductVariants($productId: BigInt!, $pagination: PaginationInput) {
    variants(productId: $productId, pagination: $pagination) {
      items {
        id
        title
        price
        compare_at
        sku
      }
      meta {
        currentPage
        hasNextPage
        hasPreviousPage
        totalCount
        totalPages
      }
    }
  }
`;

import { useGraphQLQuery } from "@sw-ecom360/component-library/gql";
import { useParams } from "react-router";
import ProductDetailForm from "./components/productDetail/ProductDetailForm";
import ProductDetailSkeleton from "./components/productDetail/ProductDetailSkeleton";
import { GET_PRODUCT } from "./graphQL/getProduct";
import type { Product } from "~/generated/graphql";

export interface IProductData {
  product: Product;
}

function ProductDetailsRoute() {
  const { id } = useParams();
  const productId = Number(id);

  const {
    data: productData,
    error: productError,
    isLoading,
  } = useGraphQLQuery<IProductData>(GET_PRODUCT, {
    productId: productId,
  });

  const product = productData?.product;

  if (isLoading) {
    return <ProductDetailSkeleton />;
  }

  if (productError || !product) {
    throw new Error("Something happened while fetching the product details");
  }

  return <ProductDetailForm product={product} />;
}

export default ProductDetailsRoute;

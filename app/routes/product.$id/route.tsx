import { useGraphQLQuery } from "@sw-ecom360/component-library/gql";
import { useParams } from "react-router";
import ProductDetailForm from "./components/ProductDetailForm";
import { GET_PRODUCT } from "./graphQL/getProduct";
import type { Product } from "~/generated/graphql";

export interface IProductData {
  product: Product;
}

function ProductDetailsRoute() {
  const { id } = useParams();
  const productId = Number(id);
  const { data: productData, error: productError } =
    useGraphQLQuery<IProductData>(
      GET_PRODUCT,
      {
        id: productId,
      },
      { suspense: true },
    );

  const product = productData?.product;

  if (productError || !product) {
    throw new Error(
      `Something happened while fetching the product details, ${productError}`,
    );
  }

  return <ProductDetailForm product={product} />;
}

export default ProductDetailsRoute;

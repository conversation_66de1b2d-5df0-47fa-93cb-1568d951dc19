import {
  Data<PERSON>rid<PERSON><PERSON>r,
  DataGridHeader,
} from "@sw-ecom360/component-library";
import { fetcher, type SWRKey } from "@sw-ecom360/component-library/gql";
import { usePagination } from "@sw-ecom360/component-library/hooks";
import { useState } from "react";
import useS<PERSON> from "swr";
import { GET_COLLECTIONS } from "../graphql/getCollections";
import { CollectionDataGrid } from "./CollectionDataGrid";
import { CollectionModal } from "./CollectionModal";
import type { IntentionalAny } from "node_modules/@sw-ecom360/component-library/dist/types";
import type { CollectionConnection } from "~/generated/graphql";

export interface ICollectionsData {
  collections: CollectionConnection;
}

export default function CollectionOverview() {
  const [showCollectionModal, setShowCollectionModal] = useState(false);
  const { paginationModel, setPaginationModel } = usePagination();

  const { data: collectionData, mutate: refreshCollections } = useSWR<
    ICollectionsData,
    IntentionalAny,
    SWRKey
  >(
    {
      query: GET_COLLECTIONS,
      variables: {
        pagination: {
          page: paginationModel.page,
          pageSize: paginationModel.pageSize,
        },
      },
    },
    fetcher,
    { suspense: true },
  );

  return (
    <DataGridContainer>
      <DataGridHeader
        title="Collections"
        addButtonTitle="Create Collection"
        onAddButtonClick={() => setShowCollectionModal(true)}
      />
      <CollectionModal
        open={showCollectionModal}
        onClose={async (shouldRefresh) => {
          setShowCollectionModal(false);
          if (shouldRefresh) {
            await refreshCollections();
          }
        }}
      />
      <CollectionDataGrid
        collectionConnection={collectionData?.collections}
        paginationModel={paginationModel}
        setPaginationModel={setPaginationModel}
        refreshCollections={async () => {
          await refreshCollections();
        }}
      />
    </DataGridContainer>
  );
}

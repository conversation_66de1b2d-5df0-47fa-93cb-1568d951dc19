import { Dialog, DialogContent, DialogTitle } from "@mui/material";
import { useGraphQLMutation } from "@sw-ecom360/component-library/gql";
import { CREATE_COLLECTIONS as CREATE_COLLECTION } from "../graphql/createCollection";
import { CollectionForm, type ICollectionFormValues } from "./CollectionForm";
import type {
  ICreateCollectionInput,
  ICreateCollectionOutput,
} from "../interfaces/createCollection";

interface ICollectionModalProps {
  open: boolean;
  onClose: (shouldRefresh: boolean) => void;
}
export function CollectionModal({ open, onClose }: ICollectionModalProps) {
  const { trigger: createTrigger } = useGraphQLMutation<
    ICreateCollectionInput,
    ICreateCollectionOutput
  >(CREATE_COLLECTION);

  const onSubmit = async (data: ICollectionFormValues) => {
    await createTrigger({
      createCollectionInput: {
        name: data.name,
        tag: data.tag,
      },
    });
    onClose(true);
  };

  return (
    <Dialog
      open={open}
      onClose={() => {
        onClose(false);
      }}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>Create Campaign</DialogTitle>
      <DialogContent>
        <CollectionForm onSubmit={onSubmit} />
      </DialogContent>
    </Dialog>
  );
}

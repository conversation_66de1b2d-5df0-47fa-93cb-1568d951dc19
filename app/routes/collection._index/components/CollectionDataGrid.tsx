import {
  DataGrid,
  type GridColDef,
  type GridPaginationModel,
} from "@mui/x-data-grid";
import { PAGE_SIZE_OPTIONS } from "@sw-ecom360/component-library";
import { useStableRowCount } from "@sw-ecom360/component-library/hooks";
import type { CollectionConnection } from "~/generated/graphql";

const columns: GridColDef[] = [
  { field: "id", headerName: "ID", flex: 1 },
  { field: "name", headerName: "Name", flex: 1 },
  {
    field: "tag",
    headerName: "Tag",
    flex: 1,
  },
];

export interface ICollectionDataGridProps {
  collectionConnection?: CollectionConnection;
  refreshCollections: () => Promise<void>;
  paginationModel: GridPaginationModel;
  setPaginationModel: (model: GridPaginationModel) => void;
}
export function CollectionDataGrid({
  collectionConnection,
  refreshCollections,
  paginationModel,
  setPaginationModel,
}: ICollectionDataGridProps) {
  const rowCount = useStableRowCount({
    actualRowCount: collectionConnection?.meta.totalCount,
    isLoading: !collectionConnection,
  });

  return (
    <DataGrid
      rowCount={rowCount}
      columns={columns}
      rows={collectionConnection?.data || []}
      disableRowSelectionOnClick
      getRowId={(row) => row.id}
      pageSizeOptions={PAGE_SIZE_OPTIONS}
      pagination
      paginationMode="server"
      paginationModel={paginationModel}
      onPaginationModelChange={async (model: GridPaginationModel) => {
        setPaginationModel(model);
        await refreshCollections();
      }}
    />
  );
}

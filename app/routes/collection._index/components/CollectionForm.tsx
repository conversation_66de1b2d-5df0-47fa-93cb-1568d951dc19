import { Grid2, TextField } from "@mui/material";
import { Button } from "@sw-ecom360/component-library";
import { Controller, FormProvider, useForm } from "react-hook-form";

export interface ICollectionFormValues {
  name: string;
  tag: string;
}

export interface ICollectionFormPropsPayload {
  collection?: ICollectionFormValues;
  onSubmit: (data: ICollectionFormValues) => void;
}

export function CollectionForm({
  collection,
  onSubmit,
}: ICollectionFormPropsPayload) {
  const methods = useForm<ICollectionFormValues>({
    defaultValues: {
      name: collection?.name || "",
      tag: collection?.tag || "",
    },
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = methods;

  const handleFormSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    await handleSubmit((data) => {
      onSubmit(data);
      reset(data);
    })();
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleFormSubmit}>
        <Grid2 container spacing={2} sx={{ mt: 1 }}>
          <Grid2 size={{ xs: 12 }}>
            <Controller
              name="name"
              control={control}
              rules={{ required: "Name is required" }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Name"
                  fullWidth
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  variant="outlined"
                />
              )}
            />
          </Grid2>
          <Grid2 size={{ xs: 12 }}>
            <Controller
              name="tag"
              control={control}
              rules={{ required: "Tag is required" }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Tag"
                  fullWidth
                  error={!!errors.tag}
                  helperText={errors.tag?.message}
                  variant="outlined"
                />
              )}
            />
          </Grid2>
          <Grid2 size={{ xs: 12 }} container justifyContent="flex-end">
            <Button buttonType="submit" title="Submit" />
          </Grid2>
        </Grid2>
      </form>
    </FormProvider>
  );
}

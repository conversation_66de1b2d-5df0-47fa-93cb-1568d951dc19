import { <PERSON>, Dialog, DialogContent, DialogTitle } from "@mui/material";
import {
  ACCEPT_EXCEL,
  Button,
  FileDropzone,
} from "@sw-ecom360/component-library";
import { useGraphQLMutation } from "@sw-ecom360/component-library/gql";
import { useAlertMessage } from "@sw-ecom360/component-library/layout";
import { Controller, FormProvider, useForm } from "react-hook-form";
import { UPLOAD_MASTER_FILE } from "../graphQL/uploadMasterFile";
import type { IMasterFileInput } from "../interfaces/uploadMasterFile";
import type { IntentionalAny } from "@sw-ecom360/component-library/types";

interface IMasterFileUploadValues {
  files: File[];
}

interface IMasterFileImportDialogProps {
  open: boolean;
  onClose: () => void;
}

export function MasterFileUploadDialog({
  open,
  onClose,
}: IMasterFileImportDialogProps) {
  const showAlert = useAlertMessage();
  const form = useForm<IMasterFileUploadValues>({
    defaultValues: {
      files: [],
    },
  });

  const { control, handleSubmit, reset } = form;

  const { trigger: uploadMasterFile } = useGraphQLMutation<
    IMasterFileInput,
    boolean
  >(UPLOAD_MASTER_FILE);

  const onFormSubmit = async (data: IMasterFileUploadValues) => {
    await Promise.all(
      data.files.map(async (file) => {
        try {
          await uploadMasterFile({ file });
          showAlert({
            message: `File ${file.name} uploaded successfully`,
            severity: "success",
          });
          handleClose();
        } catch (error: IntentionalAny) {
          showAlert({ message: error.message, severity: "error" });
        }
      }),
    );
  };

  const handleClose = () => {
    onClose();
    reset();
  };
  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>Upload Master File</DialogTitle>
      <DialogContent>
        <FormProvider {...form}>
          <form onSubmit={handleSubmit(onFormSubmit)}>
            <Controller
              name="files"
              control={control}
              rules={{
                validate: (val) =>
                  val.length > 0 || "At least one file is required",
              }}
              render={({ field, fieldState }) => (
                <FileDropzone
                  accept={ACCEPT_EXCEL}
                  dropZoneText={
                    "Drag and drop excel files here, or click to select files"
                  }
                  error={fieldState.error?.message}
                  onChange={(files) => field.onChange(files)}
                />
              )}
            />

            <Box display="flex" justifyContent="flex-end" mt={3}>
              <Button buttonType="submit" title="Submit" />
            </Box>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
}

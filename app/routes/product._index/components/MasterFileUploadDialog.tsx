import { <PERSON>, <PERSON>alog, <PERSON>alogContent, <PERSON><PERSON>T<PERSON>le, Grid2 } from "@mui/material";
import {
  FileUpload,
  Button,
  AlertMessage,
} from "@sw-ecom360/component-library";
import { useGraphQLMutation } from "@sw-ecom360/component-library/gql";
import { useState } from "react";
import { Controller, FormProvider, useForm } from "react-hook-form";
import { UPLOAD_MASTER_FILE } from "../graphQL/uploadMasterFile";
import type { IMasterFileInput } from "../interfaces/uploadMasterFile";

export interface IMasterFileUploadValues {
  file: File;
}

interface IMasterFileImportDialogProps {
  open: boolean;
  onClose: () => void;
}

export function MasterFileUploadDialog({
  open,
  onClose,
}: IMasterFileImportDialogProps) {
  const form = useForm<IMasterFileUploadValues>();
  const { control, handleSubmit } = form;
  const [error, setError] = useState<string | undefined>(undefined);

  const { trigger: uploadMasterFile } = useGraphQLMutation<
    IMasterFileInput,
    boolean
  >(UPLOAD_MASTER_FILE);

  const onFormSubmit = async (data: IMasterFileUploadValues) => {
    await uploadMasterFile({ file: data.file })
      .then(() => {
        setError(undefined);
        handleOnClose();
      })
      .catch((error: Error) => {
        setError(error.message);
      });
  };

  const handleOnClose = () => {
    onClose();
    form.reset();
  };

  return (
    <Dialog open={open} onClose={handleOnClose} maxWidth="sm" fullWidth>
      <DialogTitle>Upload Master File</DialogTitle>
      <DialogContent>
        <AlertMessage error={error} onClose={() => setError(undefined)} />
        <FormProvider {...form}>
          <form onSubmit={handleSubmit(onFormSubmit)}>
            <Grid2 container spacing={2} sx={{ mt: 1 }}>
              <Controller
                name="file"
                control={control}
                rules={{
                  required: "File is required",
                }}
                render={({ field, fieldState }) => (
                  <FileUpload
                    value={field.value}
                    onChange={field.onChange}
                    error={fieldState.error?.message}
                  />
                )}
              />
            </Grid2>
            <Box display="flex" justifyContent="flex-end" mt={2}>
              <Button buttonType="submit" title="Submit" />
            </Box>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
}

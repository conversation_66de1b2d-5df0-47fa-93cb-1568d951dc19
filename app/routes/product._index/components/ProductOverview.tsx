import { Box, CircularProgress } from "@mui/material";
import {
  DataGrid,
  type GridColDef,
  type GridPaginationModel,
} from "@mui/x-data-grid";
import {
  DataGridContainer,
  DataGridHeader,
  PAGE_SIZE_OPTIONS,
} from "@sw-ecom360/component-library";
import { useGraphQLQuery } from "@sw-ecom360/component-library/gql";
import { useStableRowCount } from "@sw-ecom360/component-library/hooks";
import { EcomError, formatDate } from "@sw-ecom360/component-library/utils";
import { useState } from "react";
import { useNavigate } from "react-router";
import { DEFAULT_PAGINATED_RESPONSE } from "~/common/constants";
import { GET_PRODUCTS } from "../graphQL/getProducts";
import { MasterFileUploadDialog } from "./MasterFileUploadDialog";
import type { IProductsData } from "../interfaces/products";

const columns: GridColDef[] = [
  { field: "id", headerName: "ID", flex: 1 },
  { field: "title", headerName: "Title", flex: 1 },
  { field: "shop_id", headerName: "Shop ID", flex: 1 },
  {
    field: "timestamp",
    headerName: "Timestamp",
    flex: 1,
    valueFormatter: (params) => formatDate(params),
  },
];

export default function ProductsOverview() {
  const [showMasterFileDialog, setShowMasterFileDialog] = useState(false);
  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
    page: 0,
    pageSize: 10,
  });
  const [searchTerm, setSearchTerm] = useState<string>("");

  const pagination = {
    page: paginationModel.page + 1,
    pageSize: paginationModel.pageSize,
  };

  // Use search query when searchTerm is provided, otherwise use regular products query
  const variables = searchTerm ? { searchTerm, pagination } : { pagination };

  const {
    data: productsData,
    mutate: refetchProducts,
    error,
    isLoading,
  } = {
    data: [],
    mutate: () => Promise.resolve(),
    error: undefined,
    isLoading: false,
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);

    // Reset to first page when searching
    setPaginationModel((prev) => ({ ...prev, page: 0 }));
  };

  const navigate = useNavigate();
  const handleRowClick = async (params: { id: bigint }) => {
    await navigate(`/product/${params.id}`);
  };

  const onFilterModelChange = ({
    quickFilterValues,
  }: {
    quickFilterValues?: string[];
  }) => {
    handleSearch(quickFilterValues?.[0] ?? "");
  };

  const rowCount = useStableRowCount({
    isLoading: !!isLoading,
    actualRowCount: productsData?.products?.meta?.totalCount,
  });

  if (error) {
    throw new EcomError("Products Error", "Failed to fetch products");
  }

  return (
    <DataGridContainer>
      <DataGridHeader
        title="Products"
        addButtonTitle="Upload Master File"
        onAddButtonClick={() => setShowMasterFileDialog(true)}
      />

      <MasterFileUploadDialog
        open={showMasterFileDialog}
        onClose={async () => {
          setShowMasterFileDialog(false);
          await refetchProducts();
        }}
      />

      <DataGrid
        loading={!!isLoading}
        filterModel={{
          items: [],
          quickFilterValues: [searchTerm],
        }}
        filterDebounceMs={500}
        onFilterModelChange={onFilterModelChange}
        columns={columns}
        rows={
          productsData?.products?.items ??
          productsData?.searchProducts?.items ??
          DEFAULT_PAGINATED_RESPONSE.items
        }
        onRowClick={(params) => handleRowClick({ id: BigInt(params.id) })}
        pagination
        paginationMode="server"
        filterMode="server"
        rowCount={rowCount}
        paginationModel={paginationModel}
        onPaginationModelChange={setPaginationModel}
        pageSizeOptions={PAGE_SIZE_OPTIONS}
        disableColumnSelector
        disableColumnFilter
        slots={{
          loadingOverlay: () => (
            <Box p={2} textAlign="center">
              <CircularProgress size={100} />
            </Box>
          ),
        }}
        slotProps={{
          toolbar: {
            showQuickFilter: true,
            csvOptions: { disableToolbarButton: true },
            printOptions: { disableToolbarButton: true },
            quickFilterProps: {
              quickFilterParser: (filterValue: string) => {
                return [filterValue];
              },
            },
          },
        }}
        showToolbar
      />
    </DataGridContainer>
  );
}

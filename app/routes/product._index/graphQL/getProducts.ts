import { gql } from "graphql-request";

export const GET_PRODUCTS = gql`
  query GetProducts($pagination: PaginationInput, $searchTerm: String) {
    products(pagination: $pagination, quickSearchQuery: $searchTerm) {
      items {
        id
        title
        shop_id
        timestamp
      }
      meta {
        currentPage
        hasNextPage
        hasPreviousPage
        totalCount
        totalPages
      }
    }
  }
`;

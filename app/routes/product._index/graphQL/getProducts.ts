import { gql } from "graphql-request";

export const GET_PRODUCTS = gql`
  query GetProducts(
    $pagination: PaginationInput!
    $where: ProductWhereInput
    $searchTerm: String
  ) {
    products(
      pagination: $pagination
      where: $where
      quickSearchQuery: $searchTerm
    ) {
      data {
        id
        productName
        media {
          url
        }
        createdAt
      }
      meta {
        currentPage
        hasNextPage
        hasPreviousPage
        totalCount
        totalPages
      }
    }
  }
`;

import type { IUserMenuItem } from "@sw-ecom360/component-library/layout";
import type { GetCurrentUserOutput } from "aws-amplify/auth";

export const getUserMenuItems = (
  user: GetCurrentUserOutput | null,
  logout: () => void,
): IUserMenuItem[] => {
  if (!user?.signInDetails?.loginId) {
    return [];
  }
  return [
    {
      type: "display",
      label: user.signInDetails?.loginId ?? "",
    },
    {
      type: "action",
      label: "Logout",
      onAction: () => {
        logout();
      },
    },
  ];
};

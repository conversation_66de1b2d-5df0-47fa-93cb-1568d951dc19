import { type RouteConfig } from "@react-router/dev/routes";
import { flatRoutes } from "@react-router/fs-routes";
import type { SideNavigationItem } from "@sw-ecom360/component-library/layout";

export const SideNavigationRoutes: SideNavigationItem[] = [
  {
    path: "/product",
    label: "Products",
  },
  {
    path: "/variant",
    label: "Variants",
  },
];

export default flatRoutes() satisfies RouteConfig;

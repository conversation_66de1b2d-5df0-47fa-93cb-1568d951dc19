import "@fontsource/roboto/300.css";
import "@fontsource/roboto/400.css";
import "@fontsource/roboto/500.css";
import "@fontsource/roboto/700.css";
import { Container, createTheme, ThemeProvider } from "@mui/material";
import {
  RootErrorMessage,
  type IRootErrorMessageProps,
} from "@sw-ecom360/component-library";
import { configureAmplifyAuth } from "@sw-ecom360/component-library/config";
import { useCurrentUser } from "@sw-ecom360/component-library/hooks";
import { MainLayout } from "@sw-ecom360/component-library/layout";
import { signOut } from "aws-amplify/auth/cognito";
import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";
import {
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useNavigate,
} from "react-router";
import { getUserMenuItems } from "./common/userMenu";
import { SideNavigationRoutes } from "./routes";
import type { Route } from "./+types/root";
import type { Theme } from "@mui/material";
import type { IntentionalAny } from "node_modules/@sw-ecom360/component-library/dist/types";

configureAmplifyAuth({
  userPoolId: import.meta.env.VITE_COGNITO_USER_POOL_ID,
  userPoolClientId: import.meta.env.VITE_COGNITO_USER_POOL_CLIENT_ID,
});

dayjs.extend(utc);
dayjs.extend(timezone);

const theme: Theme = createTheme({
  palette: {
    primary: {
      main: "#000000",
    },
    secondary: {
      main: "#f8f8f8",
    },
  },
});

export const links: Route.LinksFunction = () => [];

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="initial-scale=1, width=device-width" />
        <Meta />
        <Links />
      </head>
      <body>
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

export default function App() {
  const navigate = useNavigate();
  const { user } = useCurrentUser();

  // TODO: move to component library if these are not particular per app
  const userMenuItems = getUserMenuItems(user, async () => {
    await signOut();
    await navigate("/");
  });

  return (
    <ThemeProvider theme={theme}>
      <Container component="main" sx={{ mt: 4, mb: 4, flex: 1 }}>
        <MainLayout
          sideNavigationItems={SideNavigationRoutes}
          onNavigate={(path: string) => navigate(path ?? "/")}
          userMenuItems={userMenuItems}
        >
          <Outlet />
        </MainLayout>
      </Container>
    </ThemeProvider>
  );
}

export function ErrorBoundary({ error }: Route.ErrorBoundaryProps) {
  const navigate = useNavigate();

  const handleErrorNavigation = () => {
    return error instanceof Error ? navigate(-1) : navigate("/");
  };

  const errorContext: IRootErrorMessageProps = {
    title: "Ooopss...",
    message: "An unexpected error occurred.",
    actionButtonText: "Back to Home Page",
    onErrorAction: handleErrorNavigation,
  };

  // our custom EcomError class
  if (error instanceof Error) {
    errorContext.title = error.name;
    errorContext.message = error.message;
    errorContext.actionButtonText = `Back`;
  }

  //React-Router default error class
  if (error instanceof Object && "data" in error && !!error.data) {
    const data: IntentionalAny = error.data;

    if (data.title !== undefined) {
      errorContext.title = data.title;
      errorContext.actionButtonText = `Back to ${data.title}`;
    }
    if (data.message !== undefined) errorContext.message = data.message;
  }

  return (
    <RootErrorMessage
      title={errorContext.title}
      message={errorContext.message}
      actionButtonText={errorContext.actionButtonText}
      onErrorAction={errorContext.onErrorAction}
    />
  );
}

# Welcome to React Router!

A modern, production-ready template for building full-stack React applications using React Router.

[![Open in StackBlitz](https://developer.stackblitz.com/img/open_in_stackblitz.svg)](https://stackblitz.com/github/remix-run/react-router-templates/tree/main/default)

## Features

- 🚀 Server-side rendering
- ⚡️ Hot Module Replacement (HMR)
- 📦 Asset bundling and optimization
- 🔄 Data loading and mutations
- 🔒 TypeScript by default
- 🎉 TailwindCSS for styling
- 📖 [React Router docs](https://reactrouter.com/)

## Getting Started

### Installation

Install the dependencies:

```bash
pnpm install
```

### Development

Start the development server with HMR:

```bash
pnpm run dev
```

Your application will be available at `http://localhost:5173`.

## Building for Production

Create a production build:

```bash
pnpm run build
```

## Deployment

### Docker Deployment

This application uses a custom Docker configuration optimized for production deployment:

- `ci/Dockerfile` - Multi-stage build with Node.js 22 Alpine and Nginx Alpine
- `ci/nginx.conf` - Custom Nginx configuration for SPA routing

To build and run using Docker:

```bash
# Build the production image
docker build -f ci/Dockerfile -t fe-product-master .

# Run the container
docker run -p 8080:8080 fe-product-master
```

The application is deployed to AWS ECS Fargate with:

- Application Load Balancer for HTTPS termination
- Auto-scaling and health checks
- Integration with AWS Cognito and GraphQL backend
- Secrets management via AWS Secrets Manager

See the [architecture diagrams](docs/diagrams/README.md) for detailed deployment information.

### DIY Deployment

If you're familiar with deploying Node applications, the built-in app server is production-ready.

Make sure to deploy the output of `pnpm run build`

```
├── package.json
├── pnpm-lock.yaml
├── build/
│   ├── client/    # Static assets (served by Nginx in production)
│   └── server/    # Server-side code (for development)
```

## Styling

This template comes with [Tailwind CSS](https://tailwindcss.com/) already configured for a simple default starting experience. You can use whatever CSS framework you prefer.

---

Built with ❤️ using React Router.
